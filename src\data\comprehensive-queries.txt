دليل الاستعلامات الشامل - كل ما يحتاجه المستخدم

هذا الملف يحتوي على جميع أنواع الاستعلامات التي قد يطلبها المستخدم بلغة طبيعية.

=== تفاصيل مشتريات العملاء ===

1. "تفاصيل مشتريات محمد أحمد":
SELECT ClientName, ItemName, Quantity, Amount, UnitPrice, TheDate, BranchName
FROM tbltemp_ItemsMain 
WHERE ClientName LIKE '%محمد%' 
AND DocumentName = 'فاتورة مبيعات'
ORDER BY TheDate DESC

2. "كل مشتريات العميل أحمد":
SELECT ClientName, 
       COUNT(*) AS TotalPurchases,
       SUM(Amount) AS TotalSpent,
       SUM(Quantity) AS TotalItems,
       MIN(TheDate) AS FirstPurchase,
       MAX(TheDate) AS LastPurchase,
       COUNT(DISTINCT ItemName) AS UniqueProducts,
       COUNT(DISTINCT BranchName) AS BranchesVisited
FROM tbltemp_ItemsMain 
WHERE ClientName LIKE '%أحمد%' 
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ClientName

3. "مشتريات العميل في آخر شهر":
SELECT ClientName, ItemName, Quantity, Amount, TheDate, BranchName
FROM tbltemp_ItemsMain 
WHERE ClientName LIKE '%[اسم العميل]%' 
AND DocumentName = 'فاتورة مبيعات'
AND TheDate >= DATEADD(MONTH, -1, GETDATE())
ORDER BY TheDate DESC

4. "أكثر منتج اشتراه العميل":
SELECT ClientName, ItemName, SUM(Quantity) AS TotalBought, SUM(Amount) AS TotalSpent
FROM tbltemp_ItemsMain 
WHERE ClientName LIKE '%[اسم العميل]%' 
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ClientName, ItemName
ORDER BY TotalBought DESC

=== تفاصيل مبيعات المنتجات ===

5. "تفاصيل مبيعات آيفون":
SELECT ItemName, ClientName, Quantity, Amount, UnitPrice, TheDate, BranchName
FROM tbltemp_ItemsMain 
WHERE ItemName LIKE '%آيفون%' 
AND DocumentName = 'فاتورة مبيعات'
ORDER BY TheDate DESC

6. "إجمالي مبيعات منتج سامسونج":
SELECT ItemName,
       SUM(Quantity) AS TotalSold,
       SUM(Amount) AS TotalRevenue,
       COUNT(*) AS NumberOfSales,
       COUNT(DISTINCT ClientName) AS UniqueCustomers,
       AVG(UnitPrice) AS AvgPrice,
       MIN(TheDate) AS FirstSale,
       MAX(TheDate) AS LastSale
FROM tbltemp_ItemsMain 
WHERE ItemName LIKE '%سامسونج%' 
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ItemName

7. "من اشترى منتج آيفون":
SELECT ItemName, ClientName, Quantity, Amount, TheDate, BranchName
FROM tbltemp_ItemsMain 
WHERE ItemName LIKE '%آيفون%' 
AND DocumentName = 'فاتورة مبيعات'
ORDER BY Amount DESC

8. "مبيعات المنتج حسب الشهر":
SELECT ItemName,
       YEAR(TheDate) AS SaleYear,
       MONTH(TheDate) AS SaleMonth,
       SUM(Quantity) AS MonthlySold,
       SUM(Amount) AS MonthlyRevenue
FROM tbltemp_ItemsMain 
WHERE ItemName LIKE '%[اسم المنتج]%' 
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ItemName, YEAR(TheDate), MONTH(TheDate)
ORDER BY SaleYear DESC, SaleMonth DESC

=== العملاء والفروع ===

9. "العميل محمد من أي فرع اشترى":
SELECT ClientName, BranchName, COUNT(*) AS PurchaseCount, SUM(Amount) AS TotalSpent
FROM tbltemp_ItemsMain 
WHERE ClientName LIKE '%محمد%' 
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ClientName, BranchName
ORDER BY TotalSpent DESC

10. "عملاء فرع الرياض":
SELECT BranchName, ClientName, COUNT(*) AS Visits, SUM(Amount) AS TotalSpent
FROM tbltemp_ItemsMain 
WHERE BranchName LIKE '%الرياض%' 
AND DocumentName = 'فاتورة مبيعات'
GROUP BY BranchName, ClientName
ORDER BY TotalSpent DESC

11. "أكثر عميل في فرع جدة":
SELECT BranchName, ClientName, SUM(Amount) AS TotalSpent, COUNT(*) AS PurchaseCount
FROM tbltemp_ItemsMain 
WHERE BranchName LIKE '%جدة%' 
AND DocumentName = 'فاتورة مبيعات'
GROUP BY BranchName, ClientName
ORDER BY TotalSpent DESC

12. "العميل أحمد اشترى من كم فرع":
SELECT ClientName, 
       COUNT(DISTINCT BranchName) AS BranchesVisited,
       STRING_AGG(DISTINCT BranchName, ', ') AS BranchNames,
       SUM(Amount) AS TotalSpent
FROM tbltemp_ItemsMain 
WHERE ClientName LIKE '%أحمد%' 
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ClientName

=== تحليلات متقدمة للعملاء ===

13. "تحليل سلوك العميل محمد":
SELECT ClientName,
       COUNT(*) AS TotalPurchases,
       SUM(Amount) AS TotalSpent,
       AVG(Amount) AS AvgPurchase,
       COUNT(DISTINCT ItemName) AS UniqueProducts,
       COUNT(DISTINCT BranchName) AS BranchesVisited,
       DATEDIFF(DAY, MIN(TheDate), MAX(TheDate)) AS CustomerLifetime,
       MIN(TheDate) AS FirstPurchase,
       MAX(TheDate) AS LastPurchase
FROM tbltemp_ItemsMain 
WHERE ClientName LIKE '%محمد%' 
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ClientName

14. "العميل الأكثر إنفاقاً على منتج معين":
SELECT ItemName, ClientName, SUM(Amount) AS TotalSpent, SUM(Quantity) AS TotalBought
FROM tbltemp_ItemsMain 
WHERE ItemName LIKE '%[اسم المنتج]%' 
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ItemName, ClientName
ORDER BY TotalSpent DESC

15. "عملاء اشتروا أكثر من منتج واحد":
SELECT ClientName, 
       COUNT(DISTINCT ItemName) AS UniqueProducts,
       SUM(Amount) AS TotalSpent,
       COUNT(*) AS TotalPurchases
FROM tbltemp_ItemsMain 
WHERE DocumentName = 'فاتورة مبيعات'
GROUP BY ClientName
HAVING COUNT(DISTINCT ItemName) > 1
ORDER BY UniqueProducts DESC

=== تحليلات المنتجات المتقدمة ===

16. "أكثر منتج مبيعاً في فرع الرياض":
SELECT BranchName, ItemName, SUM(Quantity) AS TotalSold, SUM(Amount) AS TotalRevenue
FROM tbltemp_ItemsMain 
WHERE BranchName LIKE '%الرياض%' 
AND DocumentName = 'فاتورة مبيعات'
GROUP BY BranchName, ItemName
ORDER BY TotalSold DESC

17. "منتجات لم تُباع في فرع معين":
SELECT DISTINCT i1.ItemName
FROM tbltemp_ItemsMain i1
WHERE i1.ItemName NOT IN (
    SELECT DISTINCT i2.ItemName 
    FROM tbltemp_ItemsMain i2 
    WHERE i2.BranchName LIKE '%[اسم الفرع]%'
    AND i2.DocumentName = 'فاتورة مبيعات'
)
AND i1.DocumentName = 'فاتورة مبيعات'

18. "مقارنة مبيعات منتج بين الفروع":
SELECT ItemName, BranchName, 
       SUM(Quantity) AS TotalSold,
       SUM(Amount) AS TotalRevenue,
       COUNT(DISTINCT ClientName) AS UniqueCustomers
FROM tbltemp_ItemsMain 
WHERE ItemName LIKE '%[اسم المنتج]%' 
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ItemName, BranchName
ORDER BY TotalSold DESC

=== استعلامات زمنية متقدمة ===

19. "مبيعات العميل حسب الأشهر":
SELECT ClientName,
       YEAR(TheDate) AS SaleYear,
       MONTH(TheDate) AS SaleMonth,
       SUM(Amount) AS MonthlySpent,
       COUNT(*) AS MonthlyPurchases
FROM tbltemp_ItemsMain 
WHERE ClientName LIKE '%[اسم العميل]%' 
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ClientName, YEAR(TheDate), MONTH(TheDate)
ORDER BY SaleYear DESC, SaleMonth DESC

20. "آخر مشتريات العميل":
SELECT TOP 10 ClientName, ItemName, Quantity, Amount, TheDate, BranchName
FROM tbltemp_ItemsMain 
WHERE ClientName LIKE '%[اسم العميل]%' 
AND DocumentName = 'فاتورة مبيعات'
ORDER BY TheDate DESC

21. "العملاء الذين لم يشتروا منذ شهر":
SELECT ClientName, MAX(TheDate) AS LastPurchase, SUM(Amount) AS TotalSpent
FROM tbltemp_ItemsMain 
WHERE DocumentName = 'فاتورة مبيعات'
GROUP BY ClientName
HAVING MAX(TheDate) < DATEADD(MONTH, -1, GETDATE())
ORDER BY LastPurchase DESC

=== تحليلات الفروع الشاملة ===

22. "أداء فرع الرياض الشامل":
SELECT BranchName,
       COUNT(*) AS TotalTransactions,
       COUNT(DISTINCT ClientName) AS UniqueCustomers,
       COUNT(DISTINCT ItemName) AS UniqueProducts,
       SUM(Amount) AS TotalRevenue,
       AVG(Amount) AS AvgTransaction,
       SUM(Quantity) AS TotalItemsSold
FROM tbltemp_ItemsMain 
WHERE BranchName LIKE '%الرياض%' 
AND DocumentName = 'فاتورة مبيعات'
GROUP BY BranchName

23. "مقارنة أداء جميع الفروع":
SELECT BranchName,
       SUM(Amount) AS TotalRevenue,
       COUNT(*) AS TotalTransactions,
       COUNT(DISTINCT ClientName) AS UniqueCustomers,
       AVG(Amount) AS AvgTransaction,
       SUM(Quantity) AS TotalItemsSold
FROM tbltemp_ItemsMain 
WHERE DocumentName = 'فاتورة مبيعات'
GROUP BY BranchName
ORDER BY TotalRevenue DESC

24. "أفضل عميل في كل فرع":
WITH RankedCustomers AS (
    SELECT BranchName, ClientName, SUM(Amount) AS TotalSpent,
           ROW_NUMBER() OVER (PARTITION BY BranchName ORDER BY SUM(Amount) DESC) AS Rank
    FROM tbltemp_ItemsMain 
    WHERE DocumentName = 'فاتورة مبيعات'
    GROUP BY BranchName, ClientName
)
SELECT BranchName, ClientName, TotalSpent
FROM RankedCustomers
WHERE Rank = 1
ORDER BY TotalSpent DESC

=== استعلامات البحث والفلترة المتقدمة ===

25. "العثور على عميل بالاسم الجزئي":
SELECT DISTINCT ClientName,
       COUNT(*) AS TotalPurchases,
       SUM(Amount) AS TotalSpent,
       MAX(TheDate) AS LastPurchase
FROM tbltemp_ItemsMain
WHERE ClientName LIKE '%[جزء من الاسم]%'
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ClientName
ORDER BY TotalSpent DESC

26. "البحث عن منتج بالاسم الجزئي":
SELECT DISTINCT ItemName,
       SUM(Quantity) AS TotalSold,
       SUM(Amount) AS TotalRevenue,
       COUNT(DISTINCT ClientName) AS UniqueCustomers
FROM tbltemp_ItemsMain
WHERE ItemName LIKE '%[جزء من اسم المنتج]%'
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ItemName
ORDER BY TotalSold DESC

27. "عملاء اشتروا منتجات معينة":
SELECT ClientName, ItemName, SUM(Quantity) AS TotalBought, SUM(Amount) AS TotalSpent
FROM tbltemp_ItemsMain
WHERE ItemName LIKE '%آيفون%' OR ItemName LIKE '%سامسونج%'
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ClientName, ItemName
ORDER BY TotalSpent DESC

28. "منتجات اشتراها عميل معين":
SELECT ClientName, ItemName,
       SUM(Quantity) AS TotalBought,
       SUM(Amount) AS TotalSpent,
       COUNT(*) AS PurchaseFrequency,
       AVG(UnitPrice) AS AvgPrice
FROM tbltemp_ItemsMain
WHERE ClientName LIKE '%[اسم العميل]%'
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ClientName, ItemName
ORDER BY TotalSpent DESC

=== تحليلات السعر والربحية ===

29. "أغلى منتج اشتراه العميل":
SELECT ClientName, ItemName, MAX(UnitPrice) AS HighestPrice, TheDate
FROM tbltemp_ItemsMain
WHERE ClientName LIKE '%[اسم العميل]%'
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ClientName, ItemName, TheDate
ORDER BY HighestPrice DESC

30. "متوسط سعر المنتج عبر الزمن":
SELECT ItemName,
       YEAR(TheDate) AS SaleYear,
       MONTH(TheDate) AS SaleMonth,
       AVG(UnitPrice) AS AvgPrice,
       MIN(UnitPrice) AS MinPrice,
       MAX(UnitPrice) AS MaxPrice
FROM tbltemp_ItemsMain
WHERE ItemName LIKE '%[اسم المنتج]%'
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ItemName, YEAR(TheDate), MONTH(TheDate)
ORDER BY SaleYear DESC, SaleMonth DESC

31. "أكثر المنتجات ربحية":
SELECT ItemName,
       SUM(Amount) AS TotalRevenue,
       SUM(Quantity) AS TotalSold,
       AVG(UnitPrice) AS AvgPrice,
       SUM(Amount) / SUM(Quantity) AS RevenuePerUnit
FROM tbltemp_ItemsMain
WHERE DocumentName = 'فاتورة مبيعات'
AND Quantity > 0
GROUP BY ItemName
HAVING SUM(Quantity) >= 5
ORDER BY TotalRevenue DESC

=== تحليلات التكرار والولاء ===

32. "عملاء مخلصون (أكثر من 10 مشتريات)":
SELECT ClientName,
       COUNT(*) AS TotalPurchases,
       SUM(Amount) AS TotalSpent,
       AVG(Amount) AS AvgPurchase,
       DATEDIFF(DAY, MIN(TheDate), MAX(TheDate)) AS CustomerLifetime
FROM tbltemp_ItemsMain
WHERE DocumentName = 'فاتورة مبيعات'
GROUP BY ClientName
HAVING COUNT(*) >= 10
ORDER BY TotalPurchases DESC

33. "منتجات يشتريها العملاء بانتظام":
SELECT ItemName,
       COUNT(DISTINCT ClientName) AS UniqueCustomers,
       COUNT(*) AS TotalSales,
       AVG(Quantity) AS AvgQuantityPerSale
FROM tbltemp_ItemsMain
WHERE DocumentName = 'فاتورة مبيعات'
GROUP BY ItemName
HAVING COUNT(*) >= 20
ORDER BY UniqueCustomers DESC

34. "تكرار شراء العميل لنفس المنتج":
SELECT ClientName, ItemName,
       COUNT(*) AS PurchaseFrequency,
       SUM(Quantity) AS TotalBought,
       AVG(DATEDIFF(DAY, LAG(TheDate) OVER (PARTITION BY ClientName, ItemName ORDER BY TheDate), TheDate)) AS AvgDaysBetweenPurchases
FROM tbltemp_ItemsMain
WHERE ClientName LIKE '%[اسم العميل]%'
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ClientName, ItemName
HAVING COUNT(*) > 1
ORDER BY PurchaseFrequency DESC

=== تحليلات الموسمية والاتجاهات ===

35. "مبيعات حسب أيام الأسبوع":
SELECT DATENAME(WEEKDAY, TheDate) AS DayOfWeek,
       COUNT(*) AS TotalTransactions,
       SUM(Amount) AS TotalRevenue,
       AVG(Amount) AS AvgTransaction
FROM tbltemp_ItemsMain
WHERE DocumentName = 'فاتورة مبيعات'
GROUP BY DATENAME(WEEKDAY, TheDate), DATEPART(WEEKDAY, TheDate)
ORDER BY DATEPART(WEEKDAY, TheDate)

36. "اتجاه مبيعات العميل عبر الزمن":
SELECT ClientName,
       YEAR(TheDate) AS SaleYear,
       MONTH(TheDate) AS SaleMonth,
       SUM(Amount) AS MonthlySpent,
       LAG(SUM(Amount)) OVER (PARTITION BY ClientName ORDER BY YEAR(TheDate), MONTH(TheDate)) AS PreviousMonth,
       (SUM(Amount) - LAG(SUM(Amount)) OVER (PARTITION BY ClientName ORDER BY YEAR(TheDate), MONTH(TheDate))) /
       LAG(SUM(Amount)) OVER (PARTITION BY ClientName ORDER BY YEAR(TheDate), MONTH(TheDate)) * 100 AS GrowthRate
FROM tbltemp_ItemsMain
WHERE ClientName LIKE '%[اسم العميل]%'
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ClientName, YEAR(TheDate), MONTH(TheDate)
ORDER BY SaleYear DESC, SaleMonth DESC

=== استعلامات المقارنة المتقدمة ===

37. "مقارنة عميلين":
SELECT ClientName,
       SUM(Amount) AS TotalSpent,
       COUNT(*) AS TotalPurchases,
       AVG(Amount) AS AvgPurchase,
       COUNT(DISTINCT ItemName) AS UniqueProducts,
       MAX(TheDate) AS LastPurchase
FROM tbltemp_ItemsMain
WHERE (ClientName LIKE '%[العميل الأول]%' OR ClientName LIKE '%[العميل الثاني]%')
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ClientName
ORDER BY TotalSpent DESC

38. "مقارنة منتجين":
SELECT ItemName,
       SUM(Quantity) AS TotalSold,
       SUM(Amount) AS TotalRevenue,
       COUNT(DISTINCT ClientName) AS UniqueCustomers,
       AVG(UnitPrice) AS AvgPrice
FROM tbltemp_ItemsMain
WHERE (ItemName LIKE '%[المنتج الأول]%' OR ItemName LIKE '%[المنتج الثاني]%')
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ItemName
ORDER BY TotalRevenue DESC

=== استعلامات التقارير التنفيذية ===

39. "تقرير شامل للعميل":
SELECT
    'معلومات العميل' AS ReportSection,
    ClientName,
    COUNT(*) AS TotalPurchases,
    SUM(Amount) AS TotalSpent,
    AVG(Amount) AS AvgPurchase,
    COUNT(DISTINCT ItemName) AS UniqueProducts,
    COUNT(DISTINCT BranchName) AS BranchesVisited,
    MIN(TheDate) AS FirstPurchase,
    MAX(TheDate) AS LastPurchase,
    DATEDIFF(DAY, MIN(TheDate), MAX(TheDate)) AS CustomerLifetime
FROM tbltemp_ItemsMain
WHERE ClientName LIKE '%[اسم العميل]%'
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ClientName

40. "تقرير شامل للمنتج":
SELECT
    'معلومات المنتج' AS ReportSection,
    ItemName,
    SUM(Quantity) AS TotalSold,
    SUM(Amount) AS TotalRevenue,
    COUNT(*) AS NumberOfSales,
    COUNT(DISTINCT ClientName) AS UniqueCustomers,
    COUNT(DISTINCT BranchName) AS BranchesAvailable,
    AVG(UnitPrice) AS AvgPrice,
    MIN(UnitPrice) AS MinPrice,
    MAX(UnitPrice) AS MaxPrice,
    MIN(TheDate) AS FirstSale,
    MAX(TheDate) AS LastSale
FROM tbltemp_ItemsMain
WHERE ItemName LIKE '%[اسم المنتج]%'
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ItemName
