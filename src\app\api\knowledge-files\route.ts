import { NextRequest, NextResponse } from 'next/server';

// استيراد الدوال من خدمة RAG
const { 
  indexPersistentKnowledgeBase, 
  addFileToKnowledgeBase, 
  removeFileFromKnowledgeBase,
  getPersistentStats 
} = require('@/lib/persistent-rag-service.js');

// GET - عرض حالة الملفات المفهرسة
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    if (action === 'stats') {
      // عرض إحصائيات قاعدة المعرفة
      const stats = await getPersistentStats();
      return NextResponse.json({
        success: true,
        data: stats
      });
    }

    // عرض قائمة الملفات المفهرسة
    const fs = require('fs').promises;
    const path = require('path');
    
    try {
      const statusPath = path.join(process.cwd(), 'src/data/persistent-index-status.json');
      const statusData = await fs.readFile(statusPath, 'utf-8');
      const indexInfo = JSON.parse(statusData);

      return NextResponse.json({
        success: true,
        data: {
          indexed_at: indexInfo.indexed_at,
          last_updated: indexInfo.last_updated,
          total_chunks: indexInfo.total_chunks,
          total_files: indexInfo.total_files,
          processed_files: indexInfo.processed_files || [],
          status: indexInfo.status
        }
      });
    } catch (error) {
      return NextResponse.json({
        success: true,
        data: {
          indexed_at: null,
          total_chunks: 0,
          total_files: 0,
          processed_files: [],
          status: 'not_initialized'
        }
      });
    }

  } catch (error: any) {
    console.error('خطأ في عرض معلومات الملفات:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'فشل في عرض معلومات الملفات',
        details: error.message
      },
      { status: 500 }
    );
  }
}

// POST - إضافة ملف جديد أو إعادة فهرسة جميع الملفات
export async function POST(request: NextRequest) {
  try {
    const { action, fileName, files } = await request.json();

    if (action === 'add-file') {
      // إضافة ملف واحد جديد
      if (!fileName) {
        return NextResponse.json(
          { success: false, error: 'اسم الملف مطلوب' },
          { status: 400 }
        );
      }

      console.log(`📄 إضافة ملف جديد: ${fileName}`);
      const result = await addFileToKnowledgeBase(fileName);

      return NextResponse.json({
        success: true,
        message: `تم إضافة الملف ${fileName} بنجاح`,
        data: result
      });

    } else if (action === 'reindex-all') {
      // إعادة فهرسة جميع الملفات
      console.log('🔄 إعادة فهرسة جميع الملفات...');
      
      const filesToIndex = files || [
        'rga.txt',
        'failed-queries.txt',
        'query-improvements.txt',
        'business-rules.txt',
        'sql-examples.txt'
      ];

      await indexPersistentKnowledgeBase(filesToIndex);

      return NextResponse.json({
        success: true,
        message: 'تم إعادة فهرسة جميع الملفات بنجاح',
        data: { files: filesToIndex }
      });

    } else {
      return NextResponse.json(
        { success: false, error: 'إجراء غير صحيح' },
        { status: 400 }
      );
    }

  } catch (error: any) {
    console.error('خطأ في معالجة الملفات:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'فشل في معالجة الملفات',
        details: error.message
      },
      { status: 500 }
    );
  }
}

// DELETE - حذف ملف من قاعدة المعرفة
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const fileName = searchParams.get('file');

    if (!fileName) {
      return NextResponse.json(
        { success: false, error: 'اسم الملف مطلوب' },
        { status: 400 }
      );
    }

    console.log(`🗑️ حذف الملف: ${fileName}`);
    const result = await removeFileFromKnowledgeBase(fileName);

    return NextResponse.json({
      success: true,
      message: `تم حذف الملف ${fileName} بنجاح`,
      data: result
    });

  } catch (error: any) {
    console.error('خطأ في حذف الملف:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'فشل في حذف الملف',
        details: error.message
      },
      { status: 500 }
    );
  }
}
