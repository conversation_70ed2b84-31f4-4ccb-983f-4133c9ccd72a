const { RecursiveCharacterTextSplitter } = require('langchain/text_splitter');
const { Document } = require('langchain/document');
const fs = require('fs').promises;
const path = require('path');

// نظام embeddings محلي متطور مع حفظ دائم
class PersistentEmbeddings {
  constructor() {
    this.dimension = 512;
    this.documents = [];
    this.embeddings = [];
    this.metadata = [];
  }

  async embedDocuments(texts) {
    return texts.map(text => this.createAdvancedEmbedding(text));
  }

  async embedQuery(text) {
    return this.createAdvancedEmbedding(text);
  }

  createAdvancedEmbedding(text) {
    const embedding = new Array(this.dimension).fill(0);
    
    // تنظيف النص
    const cleanText = text.toLowerCase().replace(/[^\u0600-\u06FF\u0750-\u077F\w\s]/g, ' ');
    const words = cleanText.split(/\s+/).filter(word => word.length > 1);
    
    // المصطلحات المهمة مع أوزان
    const importantTerms = {
      'منتج': 10, 'مبيعات': 10, 'عميل': 8, 'فاتورة': 8, 'مخزون': 7,
      'كمية': 6, 'سعر': 6, 'مبلغ': 6, 'تاريخ': 5, 'فرع': 5,
      'مورد': 5, 'موزع': 5, 'باركود': 4, 'تصنيف': 4, 'فئة': 4,
      'ItemName': 9, 'ClientName': 8, 'Amount': 8, 'Quantity': 7,
      'UnitPrice': 7, 'TheDate': 6, 'BranchName': 6, 'DocumentName': 6,
      'CategoryName': 5, 'DistributorName': 5, 'SupplierName': 5,
      'tbltemp_ItemsMain': 15, 'tbltemp_Inv_MainInvoice': 15,
      'tbltemp_Clients': 10, 'tbltemp_Suppliers': 10,
      'الجدول': 12, 'النية': 10, 'intent': 10, 'entity': 8,
      'استعلام': 9, 'query': 9, 'sql': 12, 'select': 8,
      'where': 6, 'join': 7, 'group': 6, 'order': 5,
      'أكثر': 7, 'أقل': 6, 'مقارنة': 8, 'تحليل': 9,
      'تقرير': 8, 'إحصائيات': 7, 'نتائج': 6
    };

    // حساب التكرارات
    const wordCounts = new Map();
    words.forEach(word => {
      wordCounts.set(word, (wordCounts.get(word) || 0) + 1);
    });

    // إنشاء embedding بناءً على الكلمات والأوزان
    words.forEach((word, wordIndex) => {
      const baseWeight = importantTerms[word] || 1;
      const frequency = wordCounts.get(word) || 1;
      const weight = baseWeight * Math.log(frequency + 1);
      
      // توزيع الوزن على عدة مواضع
      for (let i = 0; i < 3; i++) {
        const hash1 = this.simpleHash(word + i.toString());
        const hash2 = this.simpleHash(word + (i + 1).toString());
        
        const index1 = Math.abs(hash1) % this.dimension;
        const index2 = Math.abs(hash2) % this.dimension;
        
        embedding[index1] += weight * 0.6;
        embedding[index2] += weight * 0.4;
      }
      
      // إضافة معلومات موضعية
      const positionWeight = 1 / (wordIndex + 1);
      const posIndex = (wordIndex * 7) % this.dimension;
      embedding[posIndex] += weight * positionWeight * 0.3;
    });

    // تطبيع الـ vector
    const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
    const normalizedEmbedding = embedding.map(val => magnitude > 0 ? val / magnitude : 0);
    
    return normalizedEmbedding.map(val => val + (Math.random() - 0.5) * 0.001);
  }

  simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return hash;
  }

  // حساب التشابه بين vectors
  cosineSimilarity(vec1, vec2) {
    let dotProduct = 0;
    let norm1 = 0;
    let norm2 = 0;
    
    for (let i = 0; i < vec1.length; i++) {
      dotProduct += vec1[i] * vec2[i];
      norm1 += vec1[i] * vec1[i];
      norm2 += vec2[i] * vec2[i];
    }
    
    return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
  }

  // إضافة documents مع embeddings
  async addDocuments(documents) {
    for (const doc of documents) {
      const embedding = this.createAdvancedEmbedding(doc.pageContent);
      this.documents.push(doc.pageContent);
      this.embeddings.push(embedding);
      this.metadata.push(doc.metadata);
    }
  }

  // البحث بالتشابه
  async similaritySearchWithScore(query, limit = 5) {
    const queryEmbedding = this.createAdvancedEmbedding(query);
    const similarities = [];

    for (let i = 0; i < this.embeddings.length; i++) {
      const similarity = this.cosineSimilarity(queryEmbedding, this.embeddings[i]);
      similarities.push({
        index: i,
        similarity: similarity,
        document: {
          pageContent: this.documents[i],
          metadata: this.metadata[i]
        }
      });
    }

    // ترتيب حسب التشابه
    similarities.sort((a, b) => b.similarity - a.similarity);

    // إرجاع أفضل النتائج
    return similarities.slice(0, limit).map(item => [
      item.document,
      item.similarity
    ]);
  }

  // حفظ قاعدة البيانات
  async save(directory) {
    await fs.mkdir(directory, { recursive: true });
    
    const data = {
      documents: this.documents,
      embeddings: this.embeddings,
      metadata: this.metadata,
      dimension: this.dimension,
      saved_at: new Date().toISOString()
    };

    await fs.writeFile(
      path.join(directory, 'persistent_rag.json'),
      JSON.stringify(data, null, 2)
    );
  }

  // تحميل قاعدة البيانات
  async load(directory) {
    const dataPath = path.join(directory, 'persistent_rag.json');
    const data = JSON.parse(await fs.readFile(dataPath, 'utf-8'));
    
    this.documents = data.documents;
    this.embeddings = data.embeddings;
    this.metadata = data.metadata;
    this.dimension = data.dimension;
  }
}

// إعداد النظام
let persistentStore = null;

// تهيئة نظام RAG الدائم
async function initializePersistentRAG() {
  try {
    console.log('🚀 بدء تهيئة Persistent RAG Database...');
    
    persistentStore = new PersistentEmbeddings();
    
    // إنشاء مجلد قاعدة البيانات
    const dbPath = path.join(process.cwd(), 'persistent_database');
    try {
      await fs.access(dbPath);
    } catch {
      await fs.mkdir(dbPath, { recursive: true });
      console.log('📁 تم إنشاء مجلد قاعدة البيانات:', dbPath);
    }

    console.log('✅ تم تهيئة Persistent RAG بنجاح');
  } catch (error) {
    console.error('❌ خطأ في تهيئة Persistent RAG:', error);
    throw error;
  }
}

// استخراج metadata متقدم
function extractAdvancedMetadata(text, chunkIndex) {
  const metadata = {
    chunk_index: chunkIndex,
    length: text.length,
    type: 'knowledge_base',
    embedding_type: 'persistent_advanced',
    word_count: text.split(/\s+/).length
  };

  // استخراج نوع المحتوى
  if (text.includes('الجدول') || text.includes('Table')) {
    metadata.content_type = 'table_description';
    const tableMatches = text.match(/(?:الجدول|Table).*?:\s*(\w+)/gi);
    if (tableMatches) {
      metadata.table_names = tableMatches.map(match => 
        match.replace(/(?:الجدول|Table).*?:\s*/gi, '').trim()
      );
    }
  } else if (text.includes('النية:') || text.includes('intent:')) {
    metadata.content_type = 'intent';
    const intentMatches = text.match(/(?:النية|intent)\s*:\s*(\w+)/gi);
    if (intentMatches) {
      metadata.intent_names = intentMatches.map(match => 
        match.replace(/(?:النية|intent)\s*:\s*/gi, '').trim()
      );
    }
  } else if (text.includes('أوصاف الأعمدة')) {
    metadata.content_type = 'column_descriptions';
  } else if (text.includes('العلاقات بين الجداول')) {
    metadata.content_type = 'table_relationships';
  } else if (text.includes('أمثلة تطبيقية')) {
    metadata.content_type = 'examples';
  } else if (text.includes('الكيانات')) {
    metadata.content_type = 'entities';
  } else if (text.includes('SQL') || text.includes('SELECT')) {
    metadata.content_type = 'sql_examples';
  }

  // استخراج الجداول والأعمدة
  const tableMatches = text.match(/tbltemp_\w+/g);
  if (tableMatches) {
    metadata.mentioned_tables = [...new Set(tableMatches)];
    metadata.table_count = metadata.mentioned_tables.length;
  }

  const columnMatches = text.match(/\b[A-Z][a-zA-Z]*(?:ID|Name|Date|Amount|Quantity|Price|Code|Number)\b/g);
  if (columnMatches) {
    metadata.mentioned_columns = [...new Set(columnMatches)];
    metadata.column_count = metadata.mentioned_columns.length;
  }

  // استخراج الكلمات المفتاحية
  const keywords = [];
  const importantWords = [
    'منتج', 'مبيعات', 'عميل', 'فاتورة', 'مخزون', 'كمية', 'سعر', 'مبلغ',
    'تاريخ', 'فرع', 'مورد', 'موزع', 'باركود', 'تصنيف', 'فئة', 'استعلام',
    'تقرير', 'تحليل', 'مقارنة', 'إحصائيات'
  ];
  
  const textLower = text.toLowerCase();
  importantWords.forEach(word => {
    if (textLower.includes(word)) {
      keywords.push(word);
    }
  });
  
  if (keywords.length > 0) {
    metadata.keywords = keywords;
    metadata.keyword_count = keywords.length;
  }

  // حساب درجة الأهمية
  let importance = 1;
  if (metadata.content_type === 'table_description') importance += 3;
  if (metadata.content_type === 'intent') importance += 2;
  if (metadata.content_type === 'examples') importance += 2;
  if (metadata.mentioned_tables?.length > 0) importance += metadata.mentioned_tables.length;
  if (metadata.mentioned_columns?.length > 0) importance += metadata.mentioned_columns.length * 0.5;
  if (metadata.keywords?.length > 0) importance += metadata.keywords.length * 0.3;
  
  metadata.importance_score = importance;

  return metadata;
}

// فهرسة قاعدة المعرفة من ملفات متعددة
async function indexPersistentKnowledgeBase(knowledgeFiles = null) {
  try {
    if (!persistentStore) {
      throw new Error('Persistent RAG غير مهيأ');
    }

    console.log('📚 بدء فهرسة قاعدة المعرفة من ملفات متعددة...');

    // تحديد الملفات المراد فهرستها
    const filesToIndex = knowledgeFiles || [
      'rga.txt',
      'failed-queries.txt',
      'query-improvements.txt',
      'business-rules.txt',
      'sql-examples.txt'
    ];

    const dataPath = path.join(process.cwd(), 'src/data');
    let allDocuments = [];
    let totalSize = 0;
    const processedFiles = [];

    // معالجة كل ملف
    for (const fileName of filesToIndex) {
      const filePath = path.join(dataPath, fileName);

      try {
        // التحقق من وجود الملف
        await fs.access(filePath);

        const fileContent = await fs.readFile(filePath, 'utf-8');
        console.log(`📄 تم قراءة الملف: ${fileName} (${fileContent.length} حرف)`);

        if (fileContent.trim().length === 0) {
          console.log(`⚠️ الملف ${fileName} فارغ، سيتم تخطيه`);
          continue;
        }

        // تقسيم النص
        const textSplitter = new RecursiveCharacterTextSplitter({
          chunkSize: 1200,
          chunkOverlap: 300,
          separators: ['\n\n', '\n', '。', '؟', '!', ':', ';', '.', '?', '!']
        });

        const chunks = await textSplitter.splitText(fileContent);
        console.log(`🔪 تم تقسيم ${fileName} إلى ${chunks.length} قطعة`);

        // إنشاء Documents مع metadata محسن
        const fileDocuments = chunks.map((chunk, index) => {
          const metadata = extractAdvancedMetadata(chunk, index);
          // إضافة معلومات الملف المصدر
          metadata.source_file = fileName;
          metadata.file_type = getFileType(fileName);
          metadata.chunk_index_in_file = index;

          return new Document({
            pageContent: chunk,
            metadata
          });
        });

        allDocuments = allDocuments.concat(fileDocuments);
        totalSize += fileContent.length;
        processedFiles.push({
          name: fileName,
          size: fileContent.length,
          chunks: chunks.length
        });

      } catch (fileError) {
        if (fileError.code === 'ENOENT') {
          console.log(`⚠️ الملف ${fileName} غير موجود، سيتم تخطيه`);
        } else {
          console.error(`❌ خطأ في قراءة الملف ${fileName}:`, fileError.message);
        }
      }
    }

    if (allDocuments.length === 0) {
      throw new Error('لم يتم العثور على أي ملفات صالحة للفهرسة');
    }

    console.log(`🔄 إنشاء embeddings وحفظ قاعدة البيانات لـ ${allDocuments.length} وثيقة...`);

    // إضافة Documents
    await persistentStore.addDocuments(allDocuments);

    // حفظ قاعدة البيانات
    const dbPath = path.join(process.cwd(), 'persistent_database');
    await persistentStore.save(dbPath);

    console.log(`✅ تم حفظ قاعدة البيانات مع ${allDocuments.length} قطعة من ${processedFiles.length} ملفات`);

    // حفظ معلومات الفهرسة المحسنة
    const indexInfo = {
      indexed_at: new Date().toISOString(),
      total_chunks: allDocuments.length,
      total_files: processedFiles.length,
      embedding_type: 'persistent_advanced_multi_file',
      embedding_dimension: 512,
      database_path: dbPath,
      status: 'ready',
      processed_files: processedFiles,
      total_size: totalSize
    };

    await fs.writeFile(
      path.join(process.cwd(), 'src/data/persistent-index-status.json'),
      JSON.stringify(indexInfo, null, 2)
    );

    console.log('💾 تم حفظ معلومات الفهرسة المحسنة');
    console.log('📊 ملخص الفهرسة:');
    processedFiles.forEach(file => {
      console.log(`   📄 ${file.name}: ${file.chunks} قطعة (${file.size} حرف)`);
    });

  } catch (error) {
    console.error('❌ خطأ في فهرسة قاعدة المعرفة:', error);
    throw error;
  }
}

// دالة مساعدة لتحديد نوع الملف
function getFileType(fileName) {
  if (fileName.includes('failed')) return 'failed_queries';
  if (fileName.includes('improvement')) return 'improvements';
  if (fileName.includes('business')) return 'business_rules';
  if (fileName.includes('sql')) return 'sql_examples';
  if (fileName.includes('rga')) return 'main_knowledge';
  return 'general_knowledge';
}

// تحميل قاعدة البيانات
async function loadPersistentIndex() {
  try {
    if (!persistentStore) {
      await initializePersistentRAG();
    }

    const dbPath = path.join(process.cwd(), 'persistent_database');
    
    try {
      await fs.access(path.join(dbPath, 'persistent_rag.json'));
      console.log('📂 تحميل قاعدة البيانات من القرص...');
      await persistentStore.load(dbPath);
      console.log('✅ تم تحميل قاعدة البيانات بنجاح');
    } catch (error) {
      console.log('⚠️ لم يتم العثور على قاعدة البيانات، يجب إنشاؤها أولاً');
      throw new Error('قاعدة البيانات غير موجودة، يرجى تشغيل الفهرسة أولاً');
    }
  } catch (error) {
    console.error('❌ خطأ في تحميل قاعدة البيانات:', error);
    throw error;
  }
}

// البحث في قاعدة البيانات
async function searchPersistentKnowledge(query, limit = 5, scoreThreshold = 0.1) {
  try {
    if (!persistentStore || persistentStore.documents.length === 0) {
      await loadPersistentIndex();
    }

    console.log(`🔍 البحث في قاعدة البيانات: "${query}"`);

    const results = await persistentStore.similaritySearchWithScore(query, limit);

    console.log(`📋 تم العثور على ${results.length} نتائج`);

    const formattedResults = results
      .filter(result => result[1] >= scoreThreshold)
      .map((result, index) => ({
        content: result[0].pageContent,
        metadata: result[0].metadata,
        similarity: result[1],
        score: result[1],
        id: `persistent_result_${index}`,
        rank: index + 1
      }))
      .sort((a, b) => b.similarity - a.similarity);

    console.log(`✅ تم تنسيق ${formattedResults.length} نتائج عالية الجودة`);

    return formattedResults;
  } catch (error) {
    console.error('❌ خطأ في البحث:', error);
    return [];
  }
}

// الحصول على السياق للنموذج اللغوي
async function getPersistentContextForLLM(query) {
  try {
    console.log('🎯 الحصول على السياق للنموذج اللغوي...');

    const results = await searchPersistentKnowledge(query, 8, 0.15);

    if (results.length === 0) {
      console.log('⚠️ لم يتم العثور على نتائج مناسبة');
      return '';
    }

    // تجميع النتائج حسب نوع المحتوى
    const groupedResults = {
      table_description: [],
      intent: [],
      examples: [],
      sql_examples: [],
      others: []
    };

    results.forEach(result => {
      const type = result.metadata.content_type || 'others';
      if (groupedResults[type]) {
        groupedResults[type].push(result);
      } else {
        groupedResults.others.push(result);
      }
    });

    // تكوين السياق
    let context = '';
    
    if (groupedResults.table_description.length > 0) {
      context += '[معلومات الجداول]\n';
      context += groupedResults.table_description
        .slice(0, 2)
        .map(r => r.content)
        .join('\n\n');
      context += '\n\n---\n\n';
    }

    if (groupedResults.intent.length > 0) {
      context += '[النيات والمقاصد]\n';
      context += groupedResults.intent
        .slice(0, 2)
        .map(r => r.content)
        .join('\n\n');
      context += '\n\n---\n\n';
    }

    if (groupedResults.sql_examples.length > 0) {
      context += '[أمثلة SQL]\n';
      context += groupedResults.sql_examples
        .slice(0, 1)
        .map(r => r.content)
        .join('\n\n');
      context += '\n\n---\n\n';
    }

    if (groupedResults.examples.length > 0) {
      context += '[أمثلة تطبيقية]\n';
      context += groupedResults.examples
        .slice(0, 1)
        .map(r => r.content)
        .join('\n\n');
      context += '\n\n---\n\n';
    }

    const otherImportant = [...groupedResults.others]
      .sort((a, b) => (b.metadata.importance_score || 1) - (a.metadata.importance_score || 1))
      .slice(0, 2);

    if (otherImportant.length > 0) {
      context += '[معلومات إضافية]\n';
      context += otherImportant
        .map(r => r.content)
        .join('\n\n');
    }

    context = context.trim().replace(/\n{3,}/g, '\n\n');

    console.log(`✅ تم تكوين سياق للـ LLM بطول ${context.length} حرف من ${results.length} مصادر`);

    return context;
  } catch (error) {
    console.error('❌ خطأ في الحصول على السياق للـ LLM:', error);
    return '';
  }
}

// إحصائيات قاعدة البيانات
async function getPersistentStats() {
  try {
    try {
      const statusPath = path.join(process.cwd(), 'src/data/persistent-index-status.json');
      const statusData = await fs.readFile(statusPath, 'utf-8');
      const status = JSON.parse(statusData);
      
      const dbPath = path.join(process.cwd(), 'persistent_database');
      let filesExist = false;
      try {
        await fs.access(path.join(dbPath, 'persistent_rag.json'));
        filesExist = true;
      } catch (error) {
        filesExist = false;
      }
      
      return {
        total_documents: status.total_chunks || 0,
        indexed_at: status.indexed_at,
        embedding_type: status.embedding_type,
        embedding_dimension: status.embedding_dimension,
        database_path: status.database_path,
        file_source: status.file_source,
        file_size: status.file_size,
        files_exist: filesExist,
        status: filesExist ? 'ready' : 'needs_indexing',
        type: 'Persistent_Vector_Database'
      };
    } catch (error) {
      return {
        total_documents: 0,
        status: 'not_indexed',
        type: 'Persistent_Vector_Database',
        error: 'لم يتم العثور على معلومات الفهرسة'
      };
    }
  } catch (error) {
    console.error('❌ خطأ في الحصول على الإحصائيات:', error);
    return { error: 'فشل في الحصول على الإحصائيات' };
  }
}

// إضافة ملف جديد إلى قاعدة المعرفة الموجودة
async function addFileToKnowledgeBase(fileName) {
  try {
    if (!persistentStore) {
      await loadPersistentIndex();
    }

    console.log(`📄 إضافة ملف جديد: ${fileName}`);

    const filePath = path.join(process.cwd(), 'src/data', fileName);

    // التحقق من وجود الملف
    try {
      await fs.access(filePath);
    } catch (error) {
      throw new Error(`الملف ${fileName} غير موجود في مجلد src/data`);
    }

    const fileContent = await fs.readFile(filePath, 'utf-8');

    if (fileContent.trim().length === 0) {
      throw new Error(`الملف ${fileName} فارغ`);
    }

    console.log(`📄 تم قراءة الملف: ${fileName} (${fileContent.length} حرف)`);

    // تقسيم النص
    const textSplitter = new RecursiveCharacterTextSplitter({
      chunkSize: 1200,
      chunkOverlap: 300,
      separators: ['\n\n', '\n', '。', '؟', '!', ':', ';', '.', '?', '!']
    });

    const chunks = await textSplitter.splitText(fileContent);
    console.log(`🔪 تم تقسيم ${fileName} إلى ${chunks.length} قطعة`);

    // إنشاء Documents
    const documents = chunks.map((chunk, index) => {
      const metadata = extractAdvancedMetadata(chunk, index);
      metadata.source_file = fileName;
      metadata.file_type = getFileType(fileName);
      metadata.chunk_index_in_file = index;
      metadata.added_at = new Date().toISOString();

      return new Document({
        pageContent: chunk,
        metadata
      });
    });

    // إضافة Documents الجديدة
    await persistentStore.addDocuments(documents);

    // حفظ قاعدة البيانات المحدثة
    const dbPath = path.join(process.cwd(), 'persistent_database');
    await persistentStore.save(dbPath);

    console.log(`✅ تم إضافة ${chunks.length} قطعة جديدة من الملف ${fileName}`);

    // تحديث معلومات الفهرسة
    const statusPath = path.join(process.cwd(), 'src/data/persistent-index-status.json');
    let indexInfo;

    try {
      const statusData = await fs.readFile(statusPath, 'utf-8');
      indexInfo = JSON.parse(statusData);
    } catch (error) {
      indexInfo = {
        indexed_at: new Date().toISOString(),
        total_chunks: 0,
        total_files: 0,
        processed_files: []
      };
    }

    // تحديث الإحصائيات
    indexInfo.last_updated = new Date().toISOString();
    indexInfo.total_chunks += chunks.length;

    // إضافة معلومات الملف الجديد
    const existingFileIndex = indexInfo.processed_files?.findIndex(f => f.name === fileName);
    const fileInfo = {
      name: fileName,
      size: fileContent.length,
      chunks: chunks.length,
      added_at: new Date().toISOString()
    };

    if (existingFileIndex >= 0) {
      indexInfo.processed_files[existingFileIndex] = fileInfo;
      console.log(`🔄 تم تحديث الملف الموجود: ${fileName}`);
    } else {
      indexInfo.processed_files = indexInfo.processed_files || [];
      indexInfo.processed_files.push(fileInfo);
      indexInfo.total_files = indexInfo.processed_files.length;
      console.log(`➕ تم إضافة ملف جديد: ${fileName}`);
    }

    await fs.writeFile(statusPath, JSON.stringify(indexInfo, null, 2));

    return {
      success: true,
      fileName,
      chunksAdded: chunks.length,
      fileSize: fileContent.length,
      totalChunks: indexInfo.total_chunks,
      totalFiles: indexInfo.total_files
    };

  } catch (error) {
    console.error(`❌ خطأ في إضافة الملف ${fileName}:`, error);
    throw error;
  }
}

// حذف ملف من قاعدة المعرفة
async function removeFileFromKnowledgeBase(fileName) {
  try {
    console.log(`🗑️ حذف الملف: ${fileName} من قاعدة المعرفة`);

    // هذه العملية تتطلب إعادة بناء قاعدة البيانات بدون الملف المحدد
    // لأن النظام الحالي لا يدعم الحذف المباشر

    const statusPath = path.join(process.cwd(), 'src/data/persistent-index-status.json');
    let indexInfo;

    try {
      const statusData = await fs.readFile(statusPath, 'utf-8');
      indexInfo = JSON.parse(statusData);
    } catch (error) {
      throw new Error('لم يتم العثور على معلومات الفهرسة');
    }

    if (!indexInfo.processed_files) {
      throw new Error('لا توجد معلومات عن الملفات المفهرسة');
    }

    const fileIndex = indexInfo.processed_files.findIndex(f => f.name === fileName);
    if (fileIndex === -1) {
      throw new Error(`الملف ${fileName} غير موجود في قاعدة المعرفة`);
    }

    // إنشاء قائمة الملفات بدون الملف المحذوف
    const remainingFiles = indexInfo.processed_files
      .filter(f => f.name !== fileName)
      .map(f => f.name);

    console.log(`🔄 إعادة بناء قاعدة المعرفة بدون ${fileName}...`);

    // إعادة تهيئة المخزن
    persistentStore = new PersistentEmbeddings();

    // إعادة فهرسة الملفات المتبقية
    await indexPersistentKnowledgeBase(remainingFiles);

    console.log(`✅ تم حذف ${fileName} وإعادة بناء قاعدة المعرفة`);

    return {
      success: true,
      removedFile: fileName,
      remainingFiles: remainingFiles.length
    };

  } catch (error) {
    console.error(`❌ خطأ في حذف الملف ${fileName}:`, error);
    throw error;
  }
}

module.exports = {
  initializePersistentRAG,
  indexPersistentKnowledgeBase,
  addFileToKnowledgeBase,
  removeFileFromKnowledgeBase,
  loadPersistentIndex,
  searchPersistentKnowledge,
  getPersistentContextForLLM,
  getPersistentStats
};
