const { initializePersistentRAG, searchPersistentKnowledge, getPersistentContextForLLM, getPersistentStats, loadPersistentIndex } = require('../src/lib/persistent-rag-service.js');

async function testPersistentRAG() {
  try {
    console.log('🧪 بدء اختبار شامل لنظام Persistent RAG...\n');

    // 1. اختبار التهيئة
    console.log('1️⃣ اختبار تهيئة النظام...');
    await initializePersistentRAG();
    console.log('✅ تم تهيئة النظام بنجاح\n');

    // 2. اختبار تحميل قاعدة البيانات
    console.log('2️⃣ اختبار تحميل قاعدة البيانات...');
    try {
      await loadPersistentIndex();
      console.log('✅ تم تحميل قاعدة البيانات بنجاح\n');
    } catch (error) {
      console.log('⚠️ قاعدة البيانات غير موجودة، يجب تشغيل الإعداد أولاً');
      console.log('💡 تشغيل: npm run setup:persistent\n');
      return;
    }

    // 3. اختبار البحث الأساسي
    console.log('3️⃣ اختبار البحث الأساسي...');
    const testQueries = [
      'أكثر المنتجات مبيعاً',
      'تفاصيل جدول المنتجات',
      'العلاقات بين الجداول',
      'أمثلة استعلامات SQL',
      'مبيعات الفرع'
    ];

    for (const query of testQueries) {
      console.log(`🔍 اختبار: "${query}"`);
      const results = await searchPersistentKnowledge(query, 3);
      
      if (results.length > 0) {
        console.log(`   ✅ تم العثور على ${results.length} نتائج`);
        console.log(`   📊 أعلى تشابه: ${(results[0].similarity * 100).toFixed(1)}%`);
        console.log(`   📄 نوع المحتوى: ${results[0].metadata.content_type || 'غير محدد'}`);
        console.log(`   📝 بداية المحتوى: ${results[0].content.substring(0, 60)}...`);
      } else {
        console.log(`   ❌ لم يتم العثور على نتائج`);
      }
      console.log('');
    }

    // 4. اختبار الحصول على السياق للـ LLM
    console.log('4️⃣ اختبار الحصول على السياق للـ LLM...');
    const contextQueries = [
      'أكثر المنتجات مبيعاً',
      'تحليل مبيعات الفروع',
      'استعلام جدول العملاء'
    ];

    for (const query of contextQueries) {
      console.log(`🎯 اختبار السياق: "${query}"`);
      const context = await getPersistentContextForLLM(query);
      
      if (context.length > 0) {
        console.log(`   ✅ تم تكوين سياق بطول ${context.length} حرف`);
        console.log(`   📝 بداية السياق: ${context.substring(0, 100)}...`);
      } else {
        console.log(`   ❌ لم يتم تكوين سياق`);
      }
      console.log('');
    }

    // 5. اختبار البحث حسب نوع المحتوى
    console.log('5️⃣ اختبار البحث حسب نوع المحتوى...');
    const contentTypes = ['table_description', 'intent', 'examples'];
    
    for (const contentType of contentTypes) {
      console.log(`📋 اختبار نوع المحتوى: ${contentType}`);
      const results = await searchPersistentKnowledge('منتج مبيعات', 10);
      const filteredResults = results.filter(r => r.metadata.content_type === contentType);
      
      if (filteredResults.length > 0) {
        console.log(`   ✅ تم العثور على ${filteredResults.length} نتائج من نوع ${contentType}`);
        console.log(`   📊 أعلى تشابه: ${(filteredResults[0].similarity * 100).toFixed(1)}%`);
      } else {
        console.log(`   ⚠️ لم يتم العثور على نتائج من نوع ${contentType}`);
      }
      console.log('');
    }

    // 6. اختبار الأداء
    console.log('6️⃣ اختبار الأداء...');
    const performanceQuery = 'أكثر المنتجات مبيعاً';
    const iterations = 10;
    const times = [];

    for (let i = 0; i < iterations; i++) {
      const startTime = Date.now();
      await searchPersistentKnowledge(performanceQuery, 5);
      const endTime = Date.now();
      times.push(endTime - startTime);
    }

    const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
    console.log(`⏱️ متوسط وقت البحث: ${avgTime.toFixed(2)} مللي ثانية`);
    console.log(`📊 أسرع بحث: ${Math.min(...times)} مللي ثانية`);
    console.log(`📊 أبطأ بحث: ${Math.max(...times)} مللي ثانية`);
    console.log('');

    // 7. اختبار جودة النتائج
    console.log('7️⃣ اختبار جودة النتائج...');
    const qualityTests = [
      {
        query: 'جدول المنتجات الرئيسي',
        expectedType: 'table_description',
        expectedKeywords: ['tbltemp_ItemsMain', 'ItemName'],
        minSimilarity: 0.2
      },
      {
        query: 'مقارنة مبيعات الفروع',
        expectedType: 'intent',
        expectedKeywords: ['مقارنة', 'مبيعات'],
        minSimilarity: 0.15
      },
      {
        query: 'استعلام SQL للعملاء',
        expectedKeywords: ['SQL', 'عميل'],
        minSimilarity: 0.1
      }
    ];

    for (const test of qualityTests) {
      console.log(`🎯 اختبار جودة: "${test.query}"`);
      const results = await searchPersistentKnowledge(test.query, 3, 0.05);
      
      if (results.length > 0) {
        const topResult = results[0];
        
        // اختبار نوع المحتوى
        if (test.expectedType) {
          if (topResult.metadata.content_type === test.expectedType) {
            console.log(`   ✅ نوع المحتوى صحيح: ${test.expectedType}`);
          } else {
            console.log(`   ⚠️ نوع المحتوى غير متوقع: ${topResult.metadata.content_type}`);
          }
        }
        
        // اختبار الكلمات المفتاحية
        const hasKeywords = test.expectedKeywords.some(keyword => 
          topResult.content.toLowerCase().includes(keyword.toLowerCase())
        );
        
        if (hasKeywords) {
          console.log(`   ✅ يحتوي على الكلمات المفتاحية المتوقعة`);
        } else {
          console.log(`   ⚠️ لا يحتوي على الكلمات المفتاحية المتوقعة`);
        }
        
        // اختبار التشابه
        if (topResult.similarity >= test.minSimilarity) {
          console.log(`   ✅ نقاط التشابه مقبولة: ${(topResult.similarity * 100).toFixed(1)}%`);
        } else {
          console.log(`   ⚠️ نقاط التشابه منخفضة: ${(topResult.similarity * 100).toFixed(1)}%`);
        }
        
        console.log(`   📊 نقاط التشابه: ${topResult.similarity.toFixed(4)}`);
      } else {
        console.log(`   ❌ لم يتم العثور على نتائج`);
      }
      console.log('');
    }

    // 8. اختبار الإحصائيات
    console.log('8️⃣ اختبار الإحصائيات...');
    const stats = await getPersistentStats();
    console.log('📊 إحصائيات النظام:');
    console.log(JSON.stringify(stats, null, 2));
    console.log('');

    // 9. اختبار استمرارية البيانات
    console.log('9️⃣ اختبار استمرارية البيانات...');
    try {
      // إعادة تحميل النظام من الصفر
      const { 
        initializePersistentRAG: initNew, 
        loadPersistentIndex: loadNew,
        searchPersistentKnowledge: searchNew
      } = require('../src/lib/persistent-rag-service.js');
      
      await initNew();
      await loadNew();
      const persistenceResults = await searchNew('أكثر المنتجات مبيعاً', 2);
      
      if (persistenceResults.length > 0) {
        console.log('✅ البيانات محفوظة بشكل دائم ويمكن تحميلها');
        console.log(`📊 تم العثور على ${persistenceResults.length} نتائج بعد إعادة التحميل`);
      } else {
        console.log('❌ فشل في تحميل البيانات المحفوظة');
      }
    } catch (error) {
      console.log('❌ خطأ في اختبار الاستمرارية:', error.message);
    }
    console.log('');

    // 10. ملخص الاختبار
    console.log('🔟 ملخص الاختبار...');
    console.log('🎉 تم إكمال جميع الاختبارات بنجاح!');
    console.log('📋 ملخص النتائج:');
    console.log(`   - إجمالي المستندات: ${stats.total_documents}`);
    console.log(`   - نوع النظام: ${stats.type}`);
    console.log(`   - حالة النظام: ${stats.status}`);
    console.log(`   - متوسط وقت البحث: ${avgTime.toFixed(2)} مللي ثانية`);
    console.log(`   - قاعدة البيانات موجودة: ${stats.files_exist ? 'نعم' : 'لا'}`);
    console.log('   - جميع الاختبارات نجحت ✅');

    console.log('\n🚀 نظام Persistent RAG جاهز للاستخدام في الإنتاج!');
    console.log('💾 البيانات محفوظة بشكل دائم في: persistent_database/');
    console.log('🔗 النظام مربوط مع AI Service ويدعم النموذج اللغوي');

  } catch (error) {
    console.error('❌ خطأ في اختبار نظام Persistent RAG:', error);
    console.error('📋 تفاصيل الخطأ:', error.stack);
    process.exit(1);
  }
}

// تشغيل الاختبار
if (require.main === module) {
  testPersistentRAG();
}

module.exports = { testPersistentRAG };
