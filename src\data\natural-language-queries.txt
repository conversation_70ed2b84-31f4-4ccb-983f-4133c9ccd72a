دليل الاستعلامات باللغة الطبيعية - ترجمة كاملة

هذا الملف يحتوي على ترجمة الاستعلامات من اللغة الطبيعية إلى SQL.

=== استعلامات العملاء باللغة الطبيعية ===

الاستعلام: "تفاصيل مشتريات محمد"
المعنى: عرض جميع مشتريات العميل محمد
SQL:
SELECT ClientName, ItemName, Quantity, Amount, UnitPrice, TheDate, BranchName
FROM tbltemp_ItemsMain 
WHERE ClientName LIKE '%محمد%' 
AND DocumentName = 'فاتورة مبيعات'
ORDER BY TheDate DESC

الاستعلام: "كم أنفق أحمد"
المعنى: إجمالي مبلغ إنفاق العميل أحمد
SQL:
SELECT ClientName, SUM(Amount) AS TotalSpent
FROM tbltemp_ItemsMain 
WHERE ClientName LIKE '%أحمد%' 
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ClientName

الاستعلام: "محمد اشترى إيش"
المعنى: المنتجات التي اشتراها العميل محمد
SQL:
SELECT ClientName, ItemName, SUM(Quantity) AS TotalBought, SUM(Amount) AS TotalSpent
FROM tbltemp_ItemsMain 
WHERE ClientName LIKE '%محمد%' 
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ClientName, ItemName
ORDER BY TotalSpent DESC

الاستعلام: "آخر مشتريات سارة"
المعنى: أحدث مشتريات العميلة سارة
SQL:
SELECT TOP 10 ClientName, ItemName, Quantity, Amount, TheDate, BranchName
FROM tbltemp_ItemsMain 
WHERE ClientName LIKE '%سارة%' 
AND DocumentName = 'فاتورة مبيعات'
ORDER BY TheDate DESC

الاستعلام: "العميل الفلاني من أي فرع اشترى"
المعنى: الفروع التي اشترى منها العميل
SQL:
SELECT ClientName, BranchName, COUNT(*) AS PurchaseCount, SUM(Amount) AS TotalSpent
FROM tbltemp_ItemsMain 
WHERE ClientName LIKE '%[اسم العميل]%' 
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ClientName, BranchName
ORDER BY TotalSpent DESC

الاستعلام: "كم مرة اشترى خالد"
المعنى: عدد مرات شراء العميل خالد
SQL:
SELECT ClientName, COUNT(*) AS PurchaseCount, SUM(Amount) AS TotalSpent
FROM tbltemp_ItemsMain 
WHERE ClientName LIKE '%خالد%' 
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ClientName

=== استعلامات المنتجات باللغة الطبيعية ===

الاستعلام: "تفاصيل مبيعات آيفون"
المعنى: جميع مبيعات منتج آيفون
SQL:
SELECT ItemName, ClientName, Quantity, Amount, UnitPrice, TheDate, BranchName
FROM tbltemp_ItemsMain 
WHERE ItemName LIKE '%آيفون%' 
AND DocumentName = 'فاتورة مبيعات'
ORDER BY TheDate DESC

الاستعلام: "كم بعنا من سامسونج"
المعنى: إجمالي كمية مبيعات منتجات سامسونج
SQL:
SELECT ItemName, SUM(Quantity) AS TotalSold, SUM(Amount) AS TotalRevenue
FROM tbltemp_ItemsMain 
WHERE ItemName LIKE '%سامسونج%' 
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ItemName

الاستعلام: "مين اشترى آيفون"
المعنى: العملاء الذين اشتروا آيفون
SQL:
SELECT ItemName, ClientName, SUM(Quantity) AS TotalBought, SUM(Amount) AS TotalSpent
FROM tbltemp_ItemsMain 
WHERE ItemName LIKE '%آيفون%' 
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ItemName, ClientName
ORDER BY TotalSpent DESC

الاستعلام: "أكثر منتج مبيعاً"
المعنى: المنتج الأكثر مبيعاً من حيث الكمية
SQL:
SELECT TOP 10 ItemName, SUM(Quantity) AS TotalSold, SUM(Amount) AS TotalRevenue
FROM tbltemp_ItemsMain 
WHERE DocumentName = 'فاتورة مبيعات'
GROUP BY ItemName
ORDER BY TotalSold DESC

الاستعلام: "منتجات ما انباعت"
المعنى: المنتجات التي لم تُباع مؤخراً
SQL:
SELECT DISTINCT ItemName
FROM tbltemp_ItemsMain 
WHERE ItemName NOT IN (
    SELECT DISTINCT ItemName 
    FROM tbltemp_ItemsMain 
    WHERE DocumentName = 'فاتورة مبيعات'
    AND TheDate >= DATEADD(MONTH, -3, GETDATE())
)

الاستعلام: "سعر المنتج الفلاني"
المعنى: سعر منتج معين
SQL:
SELECT DISTINCT ItemName, UnitPrice, TheDate
FROM tbltemp_ItemsMain 
WHERE ItemName LIKE '%[اسم المنتج]%'
AND UnitPrice > 0
ORDER BY TheDate DESC

=== استعلامات الفروع باللغة الطبيعية ===

الاستعلام: "مبيعات فرع الرياض"
المعنى: إجمالي مبيعات فرع الرياض
SQL:
SELECT BranchName, SUM(Amount) AS TotalSales, COUNT(*) AS TransactionCount
FROM tbltemp_ItemsMain 
WHERE BranchName LIKE '%الرياض%' 
AND DocumentName = 'فاتورة مبيعات'
GROUP BY BranchName

الاستعلام: "أفضل فرع"
المعنى: الفرع الأكثر مبيعاً
SQL:
SELECT TOP 1 BranchName, SUM(Amount) AS TotalSales
FROM tbltemp_ItemsMain 
WHERE DocumentName = 'فاتورة مبيعات'
GROUP BY BranchName
ORDER BY TotalSales DESC

الاستعلام: "عملاء فرع جدة"
المعنى: العملاء الذين اشتروا من فرع جدة
SQL:
SELECT BranchName, ClientName, SUM(Amount) AS TotalSpent, COUNT(*) AS PurchaseCount
FROM tbltemp_ItemsMain 
WHERE BranchName LIKE '%جدة%' 
AND DocumentName = 'فاتورة مبيعات'
GROUP BY BranchName, ClientName
ORDER BY TotalSpent DESC

الاستعلام: "مقارنة الفروع"
المعنى: مقارنة أداء جميع الفروع
SQL:
SELECT BranchName, 
       SUM(Amount) AS TotalSales,
       COUNT(*) AS TransactionCount,
       COUNT(DISTINCT ClientName) AS UniqueCustomers,
       AVG(Amount) AS AvgTransaction
FROM tbltemp_ItemsMain 
WHERE DocumentName = 'فاتورة مبيعات'
GROUP BY BranchName
ORDER BY TotalSales DESC

=== استعلامات زمنية باللغة الطبيعية ===

الاستعلام: "مبيعات اليوم"
المعنى: إجمالي مبيعات اليوم الحالي
SQL:
SELECT SUM(Amount) AS TodaySales, COUNT(*) AS TransactionCount
FROM tbltemp_ItemsMain 
WHERE CAST(TheDate AS DATE) = CAST(GETDATE() AS DATE)
AND DocumentName = 'فاتورة مبيعات'

الاستعلام: "مبيعات هذا الشهر"
المعنى: إجمالي مبيعات الشهر الحالي
SQL:
SELECT SUM(Amount) AS MonthlySales, COUNT(*) AS TransactionCount
FROM tbltemp_ItemsMain 
WHERE MONTH(TheDate) = MONTH(GETDATE()) 
AND YEAR(TheDate) = YEAR(GETDATE())
AND DocumentName = 'فاتورة مبيعات'

الاستعلام: "مبيعات الأسبوع الماضي"
المعنى: مبيعات آخر 7 أيام
SQL:
SELECT SUM(Amount) AS WeeklySales, COUNT(*) AS TransactionCount
FROM tbltemp_ItemsMain 
WHERE TheDate >= DATEADD(DAY, -7, GETDATE())
AND DocumentName = 'فاتورة مبيعات'

الاستعلام: "مبيعات الشهر الماضي"
المعنى: مبيعات الشهر السابق
SQL:
SELECT SUM(Amount) AS LastMonthSales, COUNT(*) AS TransactionCount
FROM tbltemp_ItemsMain 
WHERE MONTH(TheDate) = MONTH(DATEADD(MONTH, -1, GETDATE()))
AND YEAR(TheDate) = YEAR(DATEADD(MONTH, -1, GETDATE()))
AND DocumentName = 'فاتورة مبيعات'

=== استعلامات مركبة باللغة الطبيعية ===

الاستعلام: "أكثر عميل اشترى آيفون"
المعنى: العميل الذي اشترى أكثر كمية من آيفون
SQL:
SELECT TOP 1 ClientName, ItemName, SUM(Quantity) AS TotalBought, SUM(Amount) AS TotalSpent
FROM tbltemp_ItemsMain 
WHERE ItemName LIKE '%آيفون%' 
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ClientName, ItemName
ORDER BY TotalBought DESC

الاستعلام: "أكثر منتج في فرع الرياض"
المعنى: المنتج الأكثر مبيعاً في فرع الرياض
SQL:
SELECT TOP 1 BranchName, ItemName, SUM(Quantity) AS TotalSold
FROM tbltemp_ItemsMain 
WHERE BranchName LIKE '%الرياض%' 
AND DocumentName = 'فاتورة مبيعات'
GROUP BY BranchName, ItemName
ORDER BY TotalSold DESC

الاستعلام: "محمد اشترى من فرع الرياض إيش"
المعنى: المنتجات التي اشتراها محمد من فرع الرياض
SQL:
SELECT ClientName, BranchName, ItemName, SUM(Quantity) AS TotalBought, SUM(Amount) AS TotalSpent
FROM tbltemp_ItemsMain 
WHERE ClientName LIKE '%محمد%' 
AND BranchName LIKE '%الرياض%'
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ClientName, BranchName, ItemName
ORDER BY TotalSpent DESC

الاستعلام: "عملاء اشتروا أكثر من 5 منتجات مختلفة"
المعنى: العملاء الذين اشتروا تنوع كبير من المنتجات
SQL:
SELECT ClientName, 
       COUNT(DISTINCT ItemName) AS UniqueProducts,
       SUM(Amount) AS TotalSpent
FROM tbltemp_ItemsMain 
WHERE DocumentName = 'فاتورة مبيعات'
GROUP BY ClientName
HAVING COUNT(DISTINCT ItemName) > 5
ORDER BY UniqueProducts DESC

=== استعلامات إحصائية باللغة الطبيعية ===

الاستعلام: "كم عميل عندنا"
المعنى: عدد العملاء الإجمالي
SQL:
SELECT COUNT(DISTINCT ClientName) AS TotalCustomers
FROM tbltemp_ItemsMain 
WHERE DocumentName = 'فاتورة مبيعات'

الاستعلام: "كم منتج نبيع"
المعنى: عدد المنتجات المختلفة التي نبيعها
SQL:
SELECT COUNT(DISTINCT ItemName) AS TotalProducts
FROM tbltemp_ItemsMain 
WHERE DocumentName = 'فاتورة مبيعات'

الاستعلام: "كم فرع عندنا"
المعنى: عدد الفروع الإجمالي
SQL:
SELECT COUNT(DISTINCT BranchName) AS TotalBranches
FROM tbltemp_ItemsMain 
WHERE DocumentName = 'فاتورة مبيعات'

الاستعلام: "متوسط قيمة الفاتورة"
المعنى: متوسط قيمة المعاملة الواحدة
SQL:
SELECT AVG(Amount) AS AvgTransactionValue
FROM tbltemp_ItemsMain 
WHERE DocumentName = 'فاتورة مبيعات'

=== استعلامات البحث المتقدم ===

الاستعلام: "ابحث عن عميل اسمه يحتوي على أحمد"
المعنى: البحث عن عملاء بأسماء مشابهة
SQL:
SELECT DISTINCT ClientName, 
       COUNT(*) AS TotalPurchases,
       SUM(Amount) AS TotalSpent
FROM tbltemp_ItemsMain 
WHERE ClientName LIKE '%أحمد%' 
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ClientName
ORDER BY TotalSpent DESC

الاستعلام: "منتجات تحتوي على كلمة جوال"
المعنى: البحث عن منتجات بأسماء مشابهة
SQL:
SELECT DISTINCT ItemName,
       SUM(Quantity) AS TotalSold,
       SUM(Amount) AS TotalRevenue
FROM tbltemp_ItemsMain 
WHERE ItemName LIKE '%جوال%' 
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ItemName
ORDER BY TotalSold DESC
