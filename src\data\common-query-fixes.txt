حلول المشاكل الشائعة في الاستعلامات

هذا الملف يحتوي على حلول للمشاكل الأكثر شيوعاً في استعلامات النظام.

=== مشكلة: Invalid column name 'ItemName' ===

السبب: محاولة استخدام ItemName من جدول tbltemp_Inv_MainInvoice
الحل: استخدم tbltemp_ItemsMain أو اعمل JOIN

❌ خطأ شائع:
SELECT ItemName FROM tbltemp_Inv_MainInvoice

✅ الحل الصحيح:
SELECT ItemName FROM tbltemp_ItemsMain WHERE DocumentName = 'فاتورة مبيعات'

✅ أو استخدم JOIN:
SELECT i.ItemName FROM tbltemp_Inv_MainInvoice inv
JOIN tbltemp_ItemsMain i ON inv.ItemID = i.ItemID

=== مشكلة: Invalid column name 'ClientName' ===

السبب: محاولة استخدام ClientName من جدول tbltemp_Inv_MainInvoice
الحل: استخدم tbltemp_ItemsMain

❌ خطأ شائع:
SELECT ClientName FROM tbltemp_Inv_MainInvoice

✅ الحل الصحيح:
SELECT ClientName FROM tbltemp_ItemsMain WHERE DocumentName = 'فاتورة مبيعات'

=== مشكلة: لا توجد نتائج للاستعلام ===

السبب: عدم استخدام LIKE في البحث النصي
الحل: استخدم LIKE '%نص%'

❌ خطأ شائع:
WHERE ItemName = 'آيفون'

✅ الحل الصحيح:
WHERE ItemName LIKE '%آيفون%'

=== مشكلة: نتائج كثيرة جداً ===

السبب: عدم استخدام TOP أو فلاتر
الحل: أضف TOP وفلاتر مناسبة

❌ خطأ شائع:
SELECT * FROM tbltemp_ItemsMain

✅ الحل الصحيح:
SELECT TOP 100 ItemName, Amount FROM tbltemp_ItemsMain 
WHERE DocumentName = 'فاتورة مبيعات'

=== الاستعلامات الشائعة المصححة ===

1. "أكثر منتج مبيعاً":
SELECT TOP 10 ItemName, SUM(Quantity) AS TotalSold
FROM tbltemp_ItemsMain 
WHERE DocumentName = 'فاتورة مبيعات'
GROUP BY ItemName
ORDER BY TotalSold DESC

2. "أكثر عميل شراءً":
SELECT TOP 10 ClientName, SUM(Amount) AS TotalSpent
FROM tbltemp_ItemsMain 
WHERE DocumentName = 'فاتورة مبيعات'
GROUP BY ClientName
ORDER BY TotalSpent DESC

3. "مبيعات اليوم":
SELECT SUM(Amount) AS TodaySales, COUNT(*) AS TransactionCount
FROM tbltemp_ItemsMain 
WHERE CAST(TheDate AS DATE) = CAST(GETDATE() AS DATE)
AND DocumentName = 'فاتورة مبيعات'

4. "مبيعات الشهر":
SELECT SUM(Amount) AS MonthlySales, COUNT(*) AS TransactionCount
FROM tbltemp_ItemsMain 
WHERE MONTH(TheDate) = MONTH(GETDATE()) 
AND YEAR(TheDate) = YEAR(GETDATE())
AND DocumentName = 'فاتورة مبيعات'

5. "مبيعات فرع معين":
SELECT BranchName, SUM(Amount) AS BranchSales
FROM tbltemp_ItemsMain 
WHERE BranchName LIKE '%الرياض%' 
AND DocumentName = 'فاتورة مبيعات'
GROUP BY BranchName

6. "مشتريات عميل معين":
SELECT ClientName, SUM(Amount) AS CustomerSpending, COUNT(*) AS PurchaseCount
FROM tbltemp_ItemsMain 
WHERE ClientName LIKE '%محمد%' 
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ClientName

7. "منتجات فئة معينة":
SELECT ItemName, CategoryName, SUM(Quantity) AS TotalSold
FROM tbltemp_ItemsMain 
WHERE CategoryName LIKE '%إلكترونيات%' 
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ItemName, CategoryName
ORDER BY TotalSold DESC

8. "أسعار المنتجات":
SELECT DISTINCT ItemName, UnitPrice, CategoryName
FROM tbltemp_ItemsMain 
WHERE UnitPrice > 0
ORDER BY UnitPrice DESC

9. "المخزون المتاح":
SELECT ItemName, SUM(Quantity) AS AvailableStock
FROM tbltemp_ItemsMain 
WHERE DocumentName != 'فاتورة مبيعات'
GROUP BY ItemName
HAVING SUM(Quantity) > 0
ORDER BY AvailableStock DESC

10. "مقارنة الفروع":
SELECT BranchName, 
       SUM(Amount) AS TotalSales,
       COUNT(*) AS TransactionCount,
       AVG(Amount) AS AvgTransaction
FROM tbltemp_ItemsMain 
WHERE DocumentName = 'فاتورة مبيعات'
GROUP BY BranchName
ORDER BY TotalSales DESC

=== قواعد مهمة للنجاح ===

1. استخدم دائماً tbltemp_ItemsMain للأسماء (ItemName, ClientName, BranchName)
2. استخدم DocumentName = 'فاتورة مبيعات' للمبيعات
3. استخدم LIKE '%نص%' للبحث النصي
4. استخدم TOP لتحديد عدد النتائج
5. استخدم GROUP BY مع SUM/COUNT
6. استخدم ORDER BY للترتيب
7. استخدم CAST للتواريخ
8. استخدم HAVING مع GROUP BY للفلترة

=== أمثلة للاستعلامات المتقدمة ===

11. "مبيعات آخر 7 أيام":
SELECT CAST(TheDate AS DATE) AS SaleDate, SUM(Amount) AS DailySales
FROM tbltemp_ItemsMain 
WHERE TheDate >= DATEADD(DAY, -7, GETDATE())
AND DocumentName = 'فاتورة مبيعات'
GROUP BY CAST(TheDate AS DATE)
ORDER BY SaleDate DESC

12. "أفضل منتج في كل فئة":
SELECT CategoryName, ItemName, TotalSales
FROM (
    SELECT CategoryName, ItemName, SUM(Amount) AS TotalSales,
           ROW_NUMBER() OVER (PARTITION BY CategoryName ORDER BY SUM(Amount) DESC) AS Rank
    FROM tbltemp_ItemsMain 
    WHERE DocumentName = 'فاتورة مبيعات'
    GROUP BY CategoryName, ItemName
) ranked
WHERE Rank = 1
ORDER BY TotalSales DESC

13. "العملاء الجدد هذا الشهر":
SELECT ClientName, MIN(TheDate) AS FirstPurchase, SUM(Amount) AS TotalSpent
FROM tbltemp_ItemsMain 
WHERE DocumentName = 'فاتورة مبيعات'
GROUP BY ClientName
HAVING MIN(TheDate) >= DATEADD(MONTH, -1, GETDATE())
ORDER BY TotalSpent DESC

14. "المنتجات الأكثر ربحية":
SELECT ItemName, 
       SUM(Amount) AS TotalRevenue,
       SUM(Quantity) AS TotalSold,
       SUM(Amount) / SUM(Quantity) AS RevenuePerUnit
FROM tbltemp_ItemsMain 
WHERE DocumentName = 'فاتورة مبيعات'
AND Quantity > 0
GROUP BY ItemName
HAVING SUM(Quantity) >= 10
ORDER BY TotalRevenue DESC

15. "تحليل المبيعات الشهرية":
SELECT YEAR(TheDate) AS SaleYear,
       MONTH(TheDate) AS SaleMonth,
       SUM(Amount) AS MonthlySales,
       COUNT(DISTINCT ClientName) AS UniqueCustomers,
       COUNT(DISTINCT ItemName) AS UniqueProducts
FROM tbltemp_ItemsMain 
WHERE DocumentName = 'فاتورة مبيعات'
GROUP BY YEAR(TheDate), MONTH(TheDate)
ORDER BY SaleYear DESC, SaleMonth DESC

=== نصائح لتجنب الأخطاء ===

1. تحقق من أسماء الأعمدة قبل الاستعلام
2. استخدم الجدول الصحيح لكل عمود
3. أضف فلاتر للحد من النتائج
4. استخدم GROUP BY مع الدوال التجميعية
5. تأكد من استخدام DocumentName للفلترة
6. استخدم DISTINCT لتجنب التكرار
7. اختبر الاستعلام على عينة صغيرة أولاً

=== رسائل الخطأ الشائعة وحلولها ===

خطأ: "Invalid column name"
الحل: تحقق من اسم العمود والجدول الصحيح

خطأ: "Column is invalid in the select list"
الحل: أضف العمود إلى GROUP BY

خطأ: "Cannot resolve the collation conflict"
الحل: استخدم COLLATE أو تأكد من ترميز البيانات

خطأ: "Timeout expired"
الحل: أضف فلاتر أو استخدم TOP لتقليل البيانات

خطأ: "Arithmetic overflow"
الحل: تحقق من نوع البيانات أو استخدم CAST

=== أمثلة للاستعلامات المعقدة ===

16. "مقارنة أداء المنتجات بين فترتين":
SELECT ItemName,
       SUM(CASE WHEN TheDate >= DATEADD(MONTH, -1, GETDATE()) THEN Amount ELSE 0 END) AS CurrentMonth,
       SUM(CASE WHEN TheDate >= DATEADD(MONTH, -2, GETDATE()) AND TheDate < DATEADD(MONTH, -1, GETDATE()) THEN Amount ELSE 0 END) AS LastMonth
FROM tbltemp_ItemsMain 
WHERE DocumentName = 'فاتورة مبيعات'
AND TheDate >= DATEADD(MONTH, -2, GETDATE())
GROUP BY ItemName
HAVING SUM(Amount) > 0
ORDER BY CurrentMonth DESC

17. "تحليل سلوك العملاء":
SELECT ClientName,
       COUNT(*) AS TotalPurchases,
       SUM(Amount) AS TotalSpent,
       AVG(Amount) AS AvgPurchase,
       MIN(TheDate) AS FirstPurchase,
       MAX(TheDate) AS LastPurchase,
       DATEDIFF(DAY, MIN(TheDate), MAX(TheDate)) AS CustomerLifetime
FROM tbltemp_ItemsMain 
WHERE DocumentName = 'فاتورة مبيعات'
GROUP BY ClientName
HAVING COUNT(*) >= 3
ORDER BY TotalSpent DESC
