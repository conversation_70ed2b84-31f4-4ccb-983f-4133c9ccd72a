# دليل إدارة ملفات قاعدة المعرفة

## نظرة عامة

النظام الآن يدعم إضافة ملفات متعددة لقاعدة المعرفة بدلاً من الاعتماد على ملف `rga.txt` فقط. هذا يتيح لك:

- إضافة ملفات متخصصة لأنواع مختلفة من المعرفة
- تنظيم المعلومات بشكل أفضل
- تحديث أجزاء محددة دون إعادة فهرسة كل شيء
- تتبع مصادر المعلومات

## الملفات المدعومة حالياً

### 1. `rga.txt` - قاعدة المعرفة الأساسية
- يحتوي على أوصاف الجداول والأعمدة
- النيات والكيانات الأساسية
- أمثلة SQL وحالات الاستخدام

### 2. `failed-queries.txt` - الاستعلامات الفاشلة
- الاستعلامات التي فشلت في النظام
- أسباب الفشل والحلول المقترحة
- أمثلة محسنة للاستعلامات

### 3. `query-improvements.txt` - تحسينات النظام
- التحسينات المطبقة على النظام
- التعلم من أخطاء المستخدمين
- قواعد التحسين المستمر

### 4. `business-rules.txt` - قواعد العمل
- القواعد التجارية للنظام
- منطق العمليات والحسابات
- معايير التقييم والتصنيف

### 5. `sql-examples.txt` - أمثلة SQL متقدمة
- استعلامات SQL معقدة
- حلول لمشاكل شائعة
- أفضل الممارسات

## كيفية إضافة ملف جديد

### الطريقة الأولى: عبر واجهة المستخدم

1. انتقل إلى تبويب "ملفات المعرفة" في لوحة التحكم
2. في قسم "إضافة ملف جديد"، أدخل اسم الملف
3. اضغط على "إضافة"
4. سيتم فهرسة الملف تلقائياً

### الطريقة الثانية: عبر API

```javascript
// إضافة ملف واحد
const response = await fetch('/api/knowledge-files', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    action: 'add-file',
    fileName: 'my-new-file.txt'
  })
});

// إعادة فهرسة جميع الملفات
const response = await fetch('/api/knowledge-files', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    action: 'reindex-all',
    files: ['rga.txt', 'failed-queries.txt', 'my-new-file.txt']
  })
});
```

## تنسيق الملفات

### القواعد العامة:
- استخدم ترميز UTF-8
- اكتب بوضوح ودقة
- استخدم عناوين واضحة
- أضف أمثلة عملية

### أنواع المحتوى المدعومة:

#### 1. أوصاف الجداول
```
الجدول: tbltemp_ItemsMain
الوصف: جدول المنتجات الرئيسي
الأعمدة:
- ItemID: معرف المنتج
- ItemName: اسم المنتج
```

#### 2. النيات والكيانات
```
النية: get_top_products
الوصف: عرض أكثر المنتجات مبيعاً
الكلمات المفتاحية: أكثر، منتج، مبيعاً، أفضل
```

#### 3. أمثلة SQL
```
الاستعلام: "أكثر 5 منتجات مبيعاً"
SQL:
SELECT TOP 5 ItemName, SUM(Quantity) AS TotalSold
FROM tbltemp_ItemsMain 
WHERE DocumentName = 'فاتورة مبيعات'
GROUP BY ItemName
ORDER BY TotalSold DESC
```

#### 4. قواعد العمل
```
قاعدة: حساب المبيعات
الوصف: المبيعات = مجموع المبالغ لفواتير المبيعات فقط
الشرط: DocumentName = 'فاتورة مبيعات'
```

## إدارة الملفات

### عرض الملفات المفهرسة
```javascript
const response = await fetch('/api/knowledge-files');
const data = await response.json();
console.log(data.data.processed_files);
```

### حذف ملف
```javascript
const response = await fetch('/api/knowledge-files?file=filename.txt', {
  method: 'DELETE'
});
```

### عرض الإحصائيات
```javascript
const response = await fetch('/api/knowledge-files?action=stats');
const stats = await response.json();
```

## أفضل الممارسات

### 1. تنظيم المحتوى
- اجعل كل ملف متخصص في موضوع واحد
- استخدم أسماء ملفات واضحة ومعبرة
- أضف تواريخ للتحديثات المهمة

### 2. جودة المحتوى
- اكتب بلغة واضحة ومفهومة
- أضف أمثلة عملية لكل مفهوم
- تأكد من صحة المعلومات

### 3. الصيانة
- راجع الملفات بانتظام
- احذف المعلومات القديمة أو الخاطئة
- أضف معلومات جديدة بناءً على تجارب المستخدمين

### 4. الأداء
- تجنب الملفات الكبيرة جداً (أكثر من 1MB)
- قسم المحتوى الكبير إلى ملفات متعددة
- استخدم إعادة الفهرسة بحذر

## استكشاف الأخطاء

### مشاكل شائعة:

#### 1. الملف غير موجود
```
خطأ: "الملف filename.txt غير موجود في مجلد src/data"
الحل: تأكد من وضع الملف في المجلد الصحيح
```

#### 2. الملف فارغ
```
خطأ: "الملف filename.txt فارغ"
الحل: أضف محتوى للملف قبل الفهرسة
```

#### 3. فشل الفهرسة
```
خطأ: "فشل في فهرسة قاعدة المعرفة"
الحل: تحقق من صحة تنسيق الملف وترميز UTF-8
```

### تشخيص المشاكل:

#### 1. فحص حالة النظام
```javascript
const response = await fetch('/api/knowledge-files?action=stats');
const stats = await response.json();
console.log('حالة النظام:', stats.data.status);
```

#### 2. فحص الملفات المفهرسة
```javascript
const response = await fetch('/api/knowledge-files');
const data = await response.json();
console.log('الملفات المفهرسة:', data.data.processed_files);
```

## التطوير المستقبلي

### ميزات مخططة:
- دعم ملفات JSON و XML
- فهرسة تلقائية عند تغيير الملفات
- نسخ احتياطية للملفات
- تصدير واستيراد قواعد المعرفة
- واجهة تحرير مدمجة

### كيفية المساهمة:
1. أضف ملفات معرفة جديدة
2. حسن المحتوى الموجود
3. اقترح تحسينات على النظام
4. شارك تجاربك وملاحظاتك

## الدعم

إذا واجهت أي مشاكل:
1. تحقق من سجلات النظام في وحدة التحكم
2. راجع هذا الدليل للحلول الشائعة
3. تأكد من صحة تنسيق الملفات
4. جرب إعادة فهرسة النظام

---

**ملاحظة**: هذا النظام في تطوير مستمر. تأكد من عمل نسخ احتياطية من ملفاتك المهمة قبل إجراء تغييرات كبيرة.
