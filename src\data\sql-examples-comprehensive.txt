دليل شامل للاستعلامات الصحيحة - حل مشاكل الأعمدة والجداول

هذا الملف يحتوي على الاستعلامات الصحيحة المبنية على البنية الفعلية لقاعدة البيانات.

=== قواعد مهمة للجداول ===

جدول tbltemp_ItemsMain:
- يحتوي على: ItemName, ClientName, BranchName, StoreName, CategoryName, ItemType
- يستخدم لـ: معلومات المنتجات الأساسية والمعاملات الشاملة
- العمود الرئيسي للمنتجات: ItemName
- العمود الرئيسي للعملاء: ClientName

جدول tbltemp_Inv_MainInvoice:
- يحتوي على: ItemID, ClientID, UnitPrice, Quantity, TotalAmount
- لا يحتوي على: ItemName, ClientName (فقط ID)
- يستخدم لـ: تفاصيل الفواتير والمعاملات المالية

=== الاستعلامات الصحيحة ===

1. أكثر المنتجات مبيعاً (الطريقة الصحيحة):

الاستعلام: "أكثر منتج مبيعاً" أو "اكثر منتجين مبيعاً"
SQL الصحيح:
SELECT TOP 10 ItemName, SUM(Quantity) AS TotalSold
FROM tbltemp_ItemsMain 
WHERE DocumentName = 'فاتورة مبيعات'
GROUP BY ItemName
ORDER BY TotalSold DESC

2. أكثر العملاء شراءً:

الاستعلام: "أكثر عميل شراءً" أو "أفضل العملاء"
SQL الصحيح:
SELECT TOP 10 ClientName, SUM(Amount) AS TotalSpent
FROM tbltemp_ItemsMain 
WHERE DocumentName = 'فاتورة مبيعات'
GROUP BY ClientName
ORDER BY TotalSpent DESC

3. مبيعات منتج معين:

الاستعلام: "مبيعات آيفون" أو "كم بعنا من آيفون"
SQL الصحيح:
SELECT ItemName, SUM(Quantity) AS TotalSold, SUM(Amount) AS TotalRevenue
FROM tbltemp_ItemsMain 
WHERE ItemName LIKE '%آيفون%' 
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ItemName

4. مبيعات عميل معين:

الاستعلام: "مشتريات محمد أحمد" أو "كم اشترى محمد"
SQL الصحيح:
SELECT ClientName, SUM(Amount) AS TotalSpent, COUNT(*) AS PurchaseCount
FROM tbltemp_ItemsMain 
WHERE ClientName LIKE '%محمد%' 
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ClientName

5. مبيعات فرع معين:

الاستعلام: "مبيعات فرع الرياض" أو "كم باع فرع جدة"
SQL الصحيح:
SELECT BranchName, SUM(Amount) AS TotalSales, COUNT(*) AS TransactionCount
FROM tbltemp_ItemsMain 
WHERE BranchName LIKE '%الرياض%' 
AND DocumentName = 'فاتورة مبيعات'
GROUP BY BranchName

6. مبيعات يومية/شهرية:

الاستعلام: "مبيعات اليوم" أو "مبيعات هذا الشهر"
SQL الصحيح:
-- مبيعات اليوم
SELECT SUM(Amount) AS DailySales, COUNT(*) AS TransactionCount
FROM tbltemp_ItemsMain 
WHERE CAST(TheDate AS DATE) = CAST(GETDATE() AS DATE)
AND DocumentName = 'فاتورة مبيعات'

-- مبيعات الشهر الحالي
SELECT SUM(Amount) AS MonthlySales, COUNT(*) AS TransactionCount
FROM tbltemp_ItemsMain 
WHERE MONTH(TheDate) = MONTH(GETDATE()) 
AND YEAR(TheDate) = YEAR(GETDATE())
AND DocumentName = 'فاتورة مبيعات'

7. المنتجات حسب الفئة:

الاستعلام: "منتجات الإلكترونيات" أو "أجهزة الكمبيوتر"
SQL الصحيح:
SELECT ItemName, CategoryName, SUM(Quantity) AS TotalSold
FROM tbltemp_ItemsMain 
WHERE CategoryName LIKE '%إلكترونيات%' 
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ItemName, CategoryName
ORDER BY TotalSold DESC

8. المخزون المتاح:

الاستعلام: "كم متوفر من المنتج" أو "رصيد المخزون"
SQL الصحيح:
SELECT ItemName, SUM(Quantity) AS AvailableStock
FROM tbltemp_ItemsMain 
WHERE DocumentName != 'فاتورة مبيعات'
GROUP BY ItemName
HAVING SUM(Quantity) > 0
ORDER BY AvailableStock DESC

9. أسعار المنتجات:

الاستعلام: "سعر آيفون" أو "كم سعر المنتج"
SQL الصحيح:
SELECT DISTINCT ItemName, UnitPrice
FROM tbltemp_ItemsMain 
WHERE ItemName LIKE '%آيفون%'
AND UnitPrice > 0

10. مقارنة الفروع:

الاستعلام: "مقارنة مبيعات الفروع"
SQL الصحيح:
SELECT BranchName, 
       SUM(Amount) AS TotalSales,
       COUNT(*) AS TransactionCount,
       AVG(Amount) AS AvgTransaction
FROM tbltemp_ItemsMain 
WHERE DocumentName = 'فاتورة مبيعات'
GROUP BY BranchName
ORDER BY TotalSales DESC

=== استعلامات متقدمة مع JOIN ===

11. تفاصيل الفواتير (استخدام الجدولين):

الاستعلام: "تفاصيل فاتورة رقم 123"
SQL الصحيح:
SELECT i.ItemName, inv.Quantity, inv.UnitPrice, inv.TotalAmount
FROM tbltemp_Inv_MainInvoice inv
INNER JOIN tbltemp_ItemsMain i ON inv.ItemID = i.ItemID
WHERE inv.InvoiceID = 123

12. أكثر المنتجات مبيعاً من الفواتير:

الاستعلام: "أكثر منتج في الفواتير"
SQL الصحيح:
SELECT i.ItemName, SUM(inv.Quantity) AS TotalSold
FROM tbltemp_Inv_MainInvoice inv
INNER JOIN tbltemp_ItemsMain i ON inv.ItemID = i.ItemID
WHERE inv.DocumentName = 'فاتورة مبيعات'
GROUP BY i.ItemName
ORDER BY TotalSold DESC

=== استعلامات التحليل الزمني ===

13. مبيعات آخر 7 أيام:

الاستعلام: "مبيعات الأسبوع الماضي"
SQL الصحيح:
SELECT CAST(TheDate AS DATE) AS SaleDate, 
       SUM(Amount) AS DailySales
FROM tbltemp_ItemsMain 
WHERE TheDate >= DATEADD(DAY, -7, GETDATE())
AND DocumentName = 'فاتورة مبيعات'
GROUP BY CAST(TheDate AS DATE)
ORDER BY SaleDate DESC

14. مقارنة شهرية:

الاستعلام: "مقارنة مبيعات الأشهر"
SQL الصحيح:
SELECT YEAR(TheDate) AS SaleYear,
       MONTH(TheDate) AS SaleMonth,
       SUM(Amount) AS MonthlySales
FROM tbltemp_ItemsMain 
WHERE DocumentName = 'فاتورة مبيعات'
GROUP BY YEAR(TheDate), MONTH(TheDate)
ORDER BY SaleYear DESC, SaleMonth DESC

=== استعلامات البحث والفلترة ===

15. البحث في المنتجات:

الاستعلام: "ابحث عن سامسونج"
SQL الصحيح:
SELECT DISTINCT ItemName, CategoryName, UnitPrice
FROM tbltemp_ItemsMain 
WHERE ItemName LIKE '%سامسونج%'
OR CategoryName LIKE '%سامسونج%'

16. المنتجات في نطاق سعري:

الاستعلام: "منتجات أقل من 1000 ريال"
SQL الصحيح:
SELECT DISTINCT ItemName, UnitPrice, CategoryName
FROM tbltemp_ItemsMain 
WHERE UnitPrice < 1000 
AND UnitPrice > 0
ORDER BY UnitPrice ASC

=== قواعد مهمة للاستعلامات ===

1. استخدم دائماً tbltemp_ItemsMain للحصول على أسماء المنتجات والعملاء
2. استخدم DocumentName = 'فاتورة مبيعات' لفلترة المبيعات فقط
3. استخدم LIKE '%نص%' للبحث في النصوص
4. استخدم SUM(Amount) للمبالغ المالية
5. استخدم SUM(Quantity) للكميات
6. استخدم COUNT(*) لعدد المعاملات
7. استخدم GROUP BY مع الأعمدة النصية
8. استخدم ORDER BY للترتيب
9. استخدم TOP للحد من النتائج

=== أخطاء شائعة يجب تجنبها ===

❌ خطأ: SELECT ItemName FROM tbltemp_Inv_MainInvoice
✅ صحيح: SELECT ItemName FROM tbltemp_ItemsMain

❌ خطأ: SELECT ClientName FROM tbltemp_Inv_MainInvoice  
✅ صحيح: SELECT ClientName FROM tbltemp_ItemsMain

❌ خطأ: WHERE ItemName = 'آيفون'
✅ صحيح: WHERE ItemName LIKE '%آيفون%'

❌ خطأ: SELECT * FROM tbltemp_ItemsMain (بدون فلتر)
✅ صحيح: SELECT TOP 100 * FROM tbltemp_ItemsMain WHERE DocumentName = 'فاتورة مبيعات'

=== استعلامات تحليلية متقدمة ===

17. تحليل أداء المنتجات حسب الفترة:

الاستعلام: "أداء المنتجات هذا الشهر مقارنة بالشهر الماضي"
SQL الصحيح:
SELECT ItemName,
       SUM(CASE WHEN MONTH(TheDate) = MONTH(GETDATE()) THEN Amount ELSE 0 END) AS CurrentMonth,
       SUM(CASE WHEN MONTH(TheDate) = MONTH(DATEADD(MONTH, -1, GETDATE())) THEN Amount ELSE 0 END) AS LastMonth
FROM tbltemp_ItemsMain
WHERE DocumentName = 'فاتورة مبيعات'
AND TheDate >= DATEADD(MONTH, -2, GETDATE())
GROUP BY ItemName
HAVING SUM(Amount) > 0
ORDER BY CurrentMonth DESC

18. العملاء الأكثر نشاطاً:

الاستعلام: "العملاء الأكثر تكراراً في الشراء"
SQL الصحيح:
SELECT ClientName,
       COUNT(DISTINCT CAST(TheDate AS DATE)) AS DaysActive,
       COUNT(*) AS TotalTransactions,
       SUM(Amount) AS TotalSpent,
       AVG(Amount) AS AvgTransaction
FROM tbltemp_ItemsMain
WHERE DocumentName = 'فاتورة مبيعات'
AND TheDate >= DATEADD(MONTH, -3, GETDATE())
GROUP BY ClientName
HAVING COUNT(*) >= 5
ORDER BY DaysActive DESC, TotalSpent DESC

19. تحليل المبيعات حسب ساعات اليوم:

الاستعلام: "في أي وقت نبيع أكثر؟"
SQL الصحيح:
SELECT DATEPART(HOUR, TheDate) AS SaleHour,
       COUNT(*) AS TransactionCount,
       SUM(Amount) AS HourlySales,
       AVG(Amount) AS AvgTransactionValue
FROM tbltemp_ItemsMain
WHERE DocumentName = 'فاتورة مبيعات'
AND TheDate >= DATEADD(DAY, -30, GETDATE())
GROUP BY DATEPART(HOUR, TheDate)
ORDER BY HourlySales DESC

20. المنتجات الأكثر ربحية:

الاستعلام: "أكثر المنتجات ربحاً"
SQL الصحيح:
SELECT ItemName,
       SUM(Quantity) AS TotalSold,
       SUM(Amount) AS TotalRevenue,
       AVG(UnitPrice) AS AvgPrice,
       SUM(Amount) / SUM(Quantity) AS RevenuePerUnit
FROM tbltemp_ItemsMain
WHERE DocumentName = 'فاتورة مبيعات'
AND Quantity > 0
GROUP BY ItemName
HAVING SUM(Quantity) >= 10
ORDER BY TotalRevenue DESC

21. تحليل الموسمية:

الاستعلام: "المبيعات حسب الفصول"
SQL الصحيح:
SELECT
    CASE
        WHEN MONTH(TheDate) IN (12, 1, 2) THEN 'الشتاء'
        WHEN MONTH(TheDate) IN (3, 4, 5) THEN 'الربيع'
        WHEN MONTH(TheDate) IN (6, 7, 8) THEN 'الصيف'
        WHEN MONTH(TheDate) IN (9, 10, 11) THEN 'الخريف'
    END AS Season,
    SUM(Amount) AS SeasonalSales,
    COUNT(*) AS TransactionCount,
    COUNT(DISTINCT ItemName) AS UniqueProducts
FROM tbltemp_ItemsMain
WHERE DocumentName = 'فاتورة مبيعات'
AND YEAR(TheDate) = YEAR(GETDATE())
GROUP BY
    CASE
        WHEN MONTH(TheDate) IN (12, 1, 2) THEN 'الشتاء'
        WHEN MONTH(TheDate) IN (3, 4, 5) THEN 'الربيع'
        WHEN MONTH(TheDate) IN (6, 7, 8) THEN 'الصيف'
        WHEN MONTH(TheDate) IN (9, 10, 11) THEN 'الخريف'
    END
ORDER BY SeasonalSales DESC

22. تحليل سلوك العملاء:

الاستعلام: "تحليل أنماط شراء العملاء"
SQL الصحيح:
SELECT ClientName,
       MIN(TheDate) AS FirstPurchase,
       MAX(TheDate) AS LastPurchase,
       DATEDIFF(DAY, MIN(TheDate), MAX(TheDate)) AS CustomerLifetime,
       COUNT(DISTINCT ItemName) AS UniqueProductsBought,
       COUNT(*) AS TotalPurchases,
       SUM(Amount) AS TotalSpent
FROM tbltemp_ItemsMain
WHERE DocumentName = 'فاتورة مبيعات'
GROUP BY ClientName
HAVING COUNT(*) >= 3
ORDER BY TotalSpent DESC

23. المنتجات المتكاملة (تُباع معاً):

الاستعلام: "المنتجات التي تُباع مع بعض"
SQL الصحيح:
SELECT a.ItemName AS Product1,
       b.ItemName AS Product2,
       COUNT(*) AS TimesSoldTogether
FROM tbltemp_ItemsMain a
INNER JOIN tbltemp_ItemsMain b ON a.ClientName = b.ClientName
    AND a.TheDate = b.TheDate
    AND a.ItemName < b.ItemName
WHERE a.DocumentName = 'فاتورة مبيعات'
AND b.DocumentName = 'فاتورة مبيعات'
GROUP BY a.ItemName, b.ItemName
HAVING COUNT(*) >= 3
ORDER BY TimesSoldTogether DESC

24. تحليل الاتجاهات:

الاستعلام: "اتجاه المبيعات خلال الأشهر"
SQL الصحيح:
SELECT YEAR(TheDate) AS SaleYear,
       MONTH(TheDate) AS SaleMonth,
       SUM(Amount) AS MonthlySales,
       LAG(SUM(Amount)) OVER (ORDER BY YEAR(TheDate), MONTH(TheDate)) AS PreviousMonth,
       (SUM(Amount) - LAG(SUM(Amount)) OVER (ORDER BY YEAR(TheDate), MONTH(TheDate))) /
       LAG(SUM(Amount)) OVER (ORDER BY YEAR(TheDate), MONTH(TheDate)) * 100 AS GrowthRate
FROM tbltemp_ItemsMain
WHERE DocumentName = 'فاتورة مبيعات'
GROUP BY YEAR(TheDate), MONTH(TheDate)
ORDER BY SaleYear DESC, SaleMonth DESC

25. تحليل الفروع المتقدم:

الاستعلام: "مقارنة شاملة للفروع"
SQL الصحيح:
SELECT BranchName,
       COUNT(DISTINCT ClientName) AS UniqueCustomers,
       COUNT(DISTINCT ItemName) AS UniqueProducts,
       SUM(Amount) AS TotalSales,
       AVG(Amount) AS AvgTransactionValue,
       SUM(Quantity) AS TotalQuantitySold,
       COUNT(*) AS TotalTransactions
FROM tbltemp_ItemsMain
WHERE DocumentName = 'فاتورة مبيعات'
AND TheDate >= DATEADD(MONTH, -1, GETDATE())
GROUP BY BranchName
ORDER BY TotalSales DESC

=== استعلامات التقارير التنفيذية ===

26. تقرير الأداء اليومي:

الاستعلام: "تقرير أداء اليوم"
SQL الصحيح:
SELECT
    COUNT(*) AS TotalTransactions,
    COUNT(DISTINCT ClientName) AS UniqueCustomers,
    COUNT(DISTINCT ItemName) AS ProductsSold,
    SUM(Amount) AS TotalRevenue,
    AVG(Amount) AS AvgTransactionValue,
    SUM(Quantity) AS TotalQuantity
FROM tbltemp_ItemsMain
WHERE DocumentName = 'فاتورة مبيعات'
AND CAST(TheDate AS DATE) = CAST(GETDATE() AS DATE)

27. تقرير أفضل 10 في كل فئة:

الاستعلام: "أفضل منتج في كل فئة"
SQL الصحيح:
WITH RankedProducts AS (
    SELECT ItemName, CategoryName, SUM(Amount) AS TotalSales,
           ROW_NUMBER() OVER (PARTITION BY CategoryName ORDER BY SUM(Amount) DESC) AS Rank
    FROM tbltemp_ItemsMain
    WHERE DocumentName = 'فاتورة مبيعات'
    GROUP BY ItemName, CategoryName
)
SELECT CategoryName, ItemName, TotalSales
FROM RankedProducts
WHERE Rank = 1
ORDER BY TotalSales DESC

=== نصائح للاستعلامات المعقدة ===

1. استخدم WITH للاستعلامات المعقدة
2. استخدم CASE WHEN للتصنيفات المشروطة
3. استخدم LAG/LEAD لمقارنة الفترات
4. استخدم ROW_NUMBER للترتيب داخل المجموعات
5. استخدم HAVING بدلاً من WHERE مع GROUP BY
6. استخدم DISTINCT لتجنب التكرار
7. استخدم DATEPART لاستخراج أجزاء التاريخ
