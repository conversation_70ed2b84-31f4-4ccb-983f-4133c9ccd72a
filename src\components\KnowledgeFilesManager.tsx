'use client';

import { useState, useEffect } from 'react';
import { FileText, Plus, Trash2, RefreshCw, Database, Clock, FileCheck } from 'lucide-react';

interface ProcessedFile {
  name: string;
  size: number;
  chunks: number;
  added_at?: string;
}

interface KnowledgeStats {
  indexed_at: string | null;
  last_updated?: string;
  total_chunks: number;
  total_files: number;
  processed_files: ProcessedFile[];
  status: string;
}

export default function KnowledgeFilesManager() {
  const [stats, setStats] = useState<KnowledgeStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [newFileName, setNewFileName] = useState('');
  const [isAddingFile, setIsAddingFile] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  // تحميل إحصائيات الملفات
  const fetchStats = async () => {
    try {
      const response = await fetch('/api/knowledge-files');
      const result = await response.json();
      
      if (result.success) {
        setStats(result.data);
      } else {
        setMessage({ type: 'error', text: 'فشل في تحميل البيانات' });
      }
    } catch (error) {
      console.error('خطأ في تحميل الإحصائيات:', error);
      setMessage({ type: 'error', text: 'خطأ في الاتصال' });
    }
  };

  // إضافة ملف جديد
  const addFile = async () => {
    if (!newFileName.trim()) {
      setMessage({ type: 'error', text: 'يرجى إدخال اسم الملف' });
      return;
    }

    setIsAddingFile(true);
    try {
      const response = await fetch('/api/knowledge-files', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'add-file',
          fileName: newFileName.trim()
        })
      });

      const result = await response.json();
      
      if (result.success) {
        setMessage({ type: 'success', text: result.message });
        setNewFileName('');
        await fetchStats();
      } else {
        setMessage({ type: 'error', text: result.error });
      }
    } catch (error) {
      console.error('خطأ في إضافة الملف:', error);
      setMessage({ type: 'error', text: 'فشل في إضافة الملف' });
    } finally {
      setIsAddingFile(false);
    }
  };

  // حذف ملف
  const deleteFile = async (fileName: string) => {
    if (!confirm(`هل أنت متأكد من حذف الملف "${fileName}"؟`)) {
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`/api/knowledge-files?file=${encodeURIComponent(fileName)}`, {
        method: 'DELETE'
      });

      const result = await response.json();
      
      if (result.success) {
        setMessage({ type: 'success', text: result.message });
        await fetchStats();
      } else {
        setMessage({ type: 'error', text: result.error });
      }
    } catch (error) {
      console.error('خطأ في حذف الملف:', error);
      setMessage({ type: 'error', text: 'فشل في حذف الملف' });
    } finally {
      setIsLoading(false);
    }
  };

  // إعادة فهرسة جميع الملفات
  const reindexAll = async () => {
    if (!confirm('هل أنت متأكد من إعادة فهرسة جميع الملفات؟ قد تستغرق هذه العملية بعض الوقت.')) {
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/knowledge-files', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'reindex-all'
        })
      });

      const result = await response.json();
      
      if (result.success) {
        setMessage({ type: 'success', text: result.message });
        await fetchStats();
      } else {
        setMessage({ type: 'error', text: result.error });
      }
    } catch (error) {
      console.error('خطأ في إعادة الفهرسة:', error);
      setMessage({ type: 'error', text: 'فشل في إعادة الفهرسة' });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  // إخفاء الرسالة بعد 5 ثوان
  useEffect(() => {
    if (message) {
      const timer = setTimeout(() => setMessage(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [message]);

  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return `${bytes} بايت`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} كيلوبايت`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} ميجابايت`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('ar-SA');
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <Database className="w-6 h-6 text-blue-600" />
          <h2 className="text-xl font-bold text-gray-900">إدارة ملفات قاعدة المعرفة</h2>
        </div>
        
        <button
          onClick={reindexAll}
          disabled={isLoading}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
        >
          <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
          إعادة فهرسة الكل
        </button>
      </div>

      {/* رسائل التنبيه */}
      {message && (
        <div className={`mb-4 p-4 rounded-lg ${
          message.type === 'success' 
            ? 'bg-green-50 text-green-800 border border-green-200' 
            : 'bg-red-50 text-red-800 border border-red-200'
        }`}>
          {message.text}
        </div>
      )}

      {/* إحصائيات عامة */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <FileText className="w-5 h-5 text-blue-600" />
              <span className="text-sm font-medium text-blue-800">إجمالي الملفات</span>
            </div>
            <div className="text-2xl font-bold text-blue-900">{stats.total_files}</div>
          </div>

          <div className="bg-green-50 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Database className="w-5 h-5 text-green-600" />
              <span className="text-sm font-medium text-green-800">إجمالي القطع</span>
            </div>
            <div className="text-2xl font-bold text-green-900">{stats.total_chunks}</div>
          </div>

          <div className="bg-purple-50 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <FileCheck className="w-5 h-5 text-purple-600" />
              <span className="text-sm font-medium text-purple-800">الحالة</span>
            </div>
            <div className="text-sm font-bold text-purple-900">
              {stats.status === 'ready' ? 'جاهز' : 'غير مهيأ'}
            </div>
          </div>

          <div className="bg-orange-50 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="w-5 h-5 text-orange-600" />
              <span className="text-sm font-medium text-orange-800">آخر تحديث</span>
            </div>
            <div className="text-xs text-orange-900">
              {stats.last_updated ? formatDate(stats.last_updated) : 'غير محدد'}
            </div>
          </div>
        </div>
      )}

      {/* إضافة ملف جديد */}
      <div className="bg-gray-50 p-4 rounded-lg mb-6">
        <h3 className="text-lg font-semibold mb-3">إضافة ملف جديد</h3>
        <div className="flex gap-3">
          <input
            type="text"
            value={newFileName}
            onChange={(e) => setNewFileName(e.target.value)}
            placeholder="اسم الملف (مثل: failed-queries.txt)"
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          <button
            onClick={addFile}
            disabled={isAddingFile || !newFileName.trim()}
            className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
          >
            <Plus className="w-4 h-4" />
            إضافة
          </button>
        </div>
        <p className="text-sm text-gray-600 mt-2">
          يجب أن يكون الملف موجوداً في مجلد src/data
        </p>
      </div>

      {/* قائمة الملفات */}
      <div>
        <h3 className="text-lg font-semibold mb-3">الملفات المفهرسة</h3>
        {stats?.processed_files && stats.processed_files.length > 0 ? (
          <div className="space-y-3">
            {stats.processed_files.map((file, index) => (
              <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center gap-3">
                  <FileText className="w-5 h-5 text-gray-600" />
                  <div>
                    <div className="font-medium text-gray-900">{file.name}</div>
                    <div className="text-sm text-gray-600">
                      {file.chunks} قطعة • {formatFileSize(file.size)}
                      {file.added_at && ` • أُضيف في ${formatDate(file.added_at)}`}
                    </div>
                  </div>
                </div>
                
                <button
                  onClick={() => deleteFile(file.name)}
                  disabled={isLoading}
                  className="flex items-center gap-2 px-3 py-1 text-red-600 hover:bg-red-50 rounded-lg disabled:opacity-50"
                >
                  <Trash2 className="w-4 h-4" />
                  حذف
                </button>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            لا توجد ملفات مفهرسة حالياً
          </div>
        )}
      </div>
    </div>
  );
}
