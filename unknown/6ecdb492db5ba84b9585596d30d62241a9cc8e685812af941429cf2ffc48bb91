{"indexed_at": "2025-07-27T19:05:51.428Z", "total_chunks": 108, "total_files": 6, "embedding_type": "persistent_advanced_multi_file", "embedding_dimension": 512, "database_path": "D:\\projact\\go\\sql-ai-agent\\persistent_database", "status": "ready", "processed_files": [{"name": "rga.txt", "size": 62634, "chunks": 70}, {"name": "failed-queries.txt", "size": 3853, "chunks": 4}, {"name": "query-improvements.txt", "size": 3843, "chunks": 4}, {"name": "business-rules.txt", "size": 4620, "chunks": 5}, {"name": "comprehensive-queries.txt", "size": 14583, "chunks": 16, "added_at": "2025-07-27T19:17:55.659Z"}, {"name": "natural-language-queries.txt", "size": 8514, "chunks": 9, "added_at": "2025-07-27T19:18:16.325Z"}], "total_size": 74950, "last_updated": "2025-07-27T19:18:16.325Z"}