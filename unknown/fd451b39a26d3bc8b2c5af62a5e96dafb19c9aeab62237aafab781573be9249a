{"queries": [{"id": "5066704ad49425eebeab85fdf3d2389e", "query": "اكثر منتجين مبيعاً ", "sql": "SELECT TOP 2 tbltemp_ItemsMain.ItemName, SUM(tbltemp_ItemsMain.Quantity) AS TotalQuantity FROM tbltemp_ItemsMain WHERE tbltemp_ItemsMain.DocumentName = 'فاتورة مبيعات' GROUP BY tbltemp_ItemsMain.ItemName ORDER BY TotalQuantity DESC", "data": [{"ItemName": "قرص صلب 1TB", "TotalQuantity": 363}, {"ItemName": "ميكروويف", "TotalQuantity": 346}], "analysis": "تحليل البيانات يشير إلى أن 'قرص صلب1TB' هو المنتج الأكثر مبيعاً، يليه 'ميكروويف'. يمكن أن يكون هذا نتيجة للتسويق الفعال، الطلب السوقي، أو استراتيجية المنتج. يجب النظر في العوامل المؤثرة مثل السعر، المنافسة، وتفضيلات العملاء.", "timestamp": "2025-07-27T18:15:02.920Z", "expiresAt": "2025-07-27T19:15:02.920Z", "executionTime": 9582, "dataHash": "f6ad83bffd6c36284a225f2385bf8fb6"}, {"id": "9c9c78ba7cbbb2259252df45bcbfa8ac", "query": "تفاصيل مبيعات منتج ميكروويف", "sql": "SELECT TOP 10 tbltemp_ItemsMain.ItemName, tbltemp_ItemsMain.TheDate, SUM(tbltemp_ItemsMain.Quantity) AS TotalQuantity FROM tbltemp_ItemsMain WHERE tbltemp_ItemsMain.ItemName = 'ميكروويف' GROUP BY tbltemp_ItemsMain.ItemName, tbltemp_ItemsMain.TheDate ORDER BY tbltemp_ItemsMain.TheDate", "data": [{"ItemName": "ميكروويف", "TheDate": "2025-04-23T00:55:01.873Z", "TotalQuantity": 1}, {"ItemName": "ميكروويف", "TheDate": "2025-04-23T06:16:13.163Z", "TotalQuantity": 1}, {"ItemName": "ميكروويف", "TheDate": "2025-04-23T15:38:46.560Z", "TotalQuantity": 1}, {"ItemName": "ميكروويف", "TheDate": "2025-04-23T16:42:21.020Z", "TotalQuantity": 4}, {"ItemName": "ميكروويف", "TheDate": "2025-04-23T17:12:55.163Z", "TotalQuantity": 3}, {"ItemName": "ميكروويف", "TheDate": "2025-04-24T05:25:00.480Z", "TotalQuantity": 1}, {"ItemName": "ميكروويف", "TheDate": "2025-04-25T01:54:57.247Z", "TotalQuantity": 1}, {"ItemName": "ميكروويف", "TheDate": "2025-04-26T10:16:56.043Z", "TotalQuantity": 5}, {"ItemName": "ميكروويف", "TheDate": "2025-04-26T11:21:24.643Z", "TotalQuantity": 1}, {"ItemName": "ميكروويف", "TheDate": "2025-04-26T11:44:28.950Z", "TotalQuantity": 3}], "analysis": "تحليل البيانات يشير إلى أن مبيعات منتج ميكروويف كانت متسقة مع زيادة طفيفة في الأيام الأخيرة. يظهر أن هناك اهتمامًا متزايدًا بشراء هذا المنتج.", "timestamp": "2025-07-27T18:15:29.680Z", "expiresAt": "2025-07-27T19:15:29.680Z", "executionTime": 6991, "dataHash": "b1ab3d3ef1b2fb3c6db03b175e805245"}, {"id": "c0f70c3b36a659219dbd4c2eb8ff8f10", "query": "اكثر عشرة عملاء شراء ", "sql": "SELECT TOP 10 tbltemp_ItemsMain.ClientName, SUM(tbltemp_ItemsMain.Amount) AS TotalSpent FROM tbltemp_ItemsMain WHERE tbltemp_ItemsMain.DocumentName = 'فاتورة مبيعات' AND tbltemp_ItemsMain.Amount > 0 GROUP BY tbltemp_ItemsMain.ClientName ORDER BY TotalSpent DESC", "data": [{"ClientName": "سمر فهد", "TotalSpent": 408215}, {"ClientName": "عباس يوسف", "TotalSpent": 362350}, {"ClientName": "سليمان صالح", "TotalSpent": 334705}, {"ClientName": "جمي<PERSON>ة خليل", "TotalSpent": 305210}, {"ClientName": "عبير زاهر", "TotalSpent": 293655}, {"ClientName": "ماج<PERSON> سعيد", "TotalSpent": 285550}, {"ClientName": "علي عبدالله", "TotalSpent": 272300}, {"ClientName": "ريم سلطان", "TotalSpent": 271690}, {"ClientName": "منيرة عبد الله", "TotalSpent": 263570}, {"ClientName": "<PERSON><PERSON><PERSON><PERSON> محمد", "TotalSpent": 237920}], "analysis": "تحليل البيانات يشير إلى أن أعلى 10 عملاء شراء هم من الشركات الكبيرة، حيث أن مجموع مبالغ الشراء لهؤلاء العملاء يمثل جزء كبير من إجمالي المبيعات. هناك تنوع في أسماء العملاء، مما يشير إلى وجود قاعدة عريضة من العملاء ذوي الشراء العالي.", "timestamp": "2025-07-27T18:19:17.675Z", "expiresAt": "2025-07-27T19:19:17.675Z", "executionTime": 5127, "dataHash": "cd2368be3397b325cb0dc2384d50356b"}, {"id": "6b23855a24737f5f573008a8e67e7436", "query": "مشتريات العميل سمر فهد", "sql": "SELECT TOP 10 tbltemp_ItemsMain.ClientName, tbltemp_ItemsMain.CategoryName, SUM(tbltemp_ItemsMain.Amount) AS TotalSpent FROM tbltemp_ItemsMain WHERE tbltemp_ItemsMain.ClientName = 'سمر فهد' GROUP BY tbltemp_ItemsMain.ClientName, tbltemp_ItemsMain.CategoryName ORDER BY TotalSpent DESC", "data": [{"ClientName": "سمر فهد", "CategoryName": "أجهزة إلكترونية", "TotalSpent": 236880}, {"ClientName": "سمر فهد", "CategoryName": "أجهزة منزلية", "TotalSpent": 74160}, {"ClientName": "سمر فهد", "CategoryName": "أثاث", "TotalSpent": 51600}, {"ClientName": "سمر فهد", "CategoryName": "أدوات منزلية", "TotalSpent": 12750}, {"ClientName": "سمر فهد", "CategoryName": "مستلزمات مكتبية", "TotalSpent": 3875}, {"ClientName": "سمر فهد", "CategoryName": "إكسسوارات", "TotalSpent": 3470}, {"ClientName": "سمر فهد", "CategoryName": "مواد غذائية", "TotalSpent": 1530}], "analysis": "تحليل البيانات يشير إلى أن سمر فهد أنفقت إجمالي 384265 على 7 فئات مختلفة من المنتجات. أعلى فئة من حيث الإنفاق هي الأجهزة الإلكترونية، تليها الأجهزة المنزلية ثم الأثاث. يشير هذا إلى أن العميل لديها اهتمامات متعددة ولكنها تميل أكثر نحو المنتجات التكنولوجية والمنزلية.", "timestamp": "2025-07-27T18:19:33.234Z", "expiresAt": "2025-07-27T19:19:33.234Z", "executionTime": 4678, "dataHash": "0bf49de81fd385061706676c3971cd72"}, {"id": "f6a1cea276f84e1984ee602a7c6382a3", "query": "قارن بين مبيعات ميكرويف و قرص صلب", "sql": "SELECT TOP 2 tbltemp_ItemsMain.ItemName, SUM(tbltemp_ItemsMain.Quantity) AS TotalQuantity, SUM(tbltemp_ItemsMain.Amount) AS TotalRevenue\n            FROM tbltemp_ItemsMain\n            WHERE tbltemp_ItemsMain.DocumentName = 'فاتورة مبيعات'\n            GROUP BY tbltemp_ItemsMain.ItemName\n            ORDER BY TotalQuantity DESC", "data": [{"ItemName": "قرص صلب 1TB", "TotalQuantity": 363, "TotalRevenue": 101640}, {"ItemName": "ميكروويف", "TotalQuantity": 346, "TotalRevenue": 110720}], "analysis": "يبدو أن هناك اهتمامًا كبيرًا بشراء ميكرويف مقارنةً بقرص صلب، حيث حققت ميكرويف مبيعات أعلى بكثير. قد يكون هذا بسبب التغير في احتياجات السوق أو تفضيلات العملاء.", "timestamp": "2025-07-27T18:20:15.129Z", "expiresAt": "2025-07-27T19:20:15.129Z", "executionTime": 3248, "dataHash": "2f6295da281e73b6a5c8964c97f57ecc"}, {"id": "a69ad16fd52d4930350fc536233b66d9", "query": "مبيعات الاسبوع الاخير", "sql": "SELECT TOP 5 tbltemp_ItemsMain.ItemName, SUM(tbltemp_ItemsMain.Quantity) AS TotalQuantity, SUM(tbltemp_ItemsMain.Amount) AS TotalRevenue\n            FROM tbltemp_ItemsMain\n            WHERE tbltemp_ItemsMain.DocumentName = 'فاتورة مبيعات'\n            GROUP BY tbltemp_ItemsMain.ItemName\n            ORDER BY TotalQuantity DESC", "data": [{"ItemName": "قرص صلب 1TB", "TotalQuantity": 363, "TotalRevenue": 101640}, {"ItemName": "ميكروويف", "TotalQuantity": 346, "TotalRevenue": 110720}, {"ItemName": "طاولة جانبية", "TotalQuantity": 344, "TotalRevenue": 75680}, {"ItemName": "ت<PERSON><PERSON> خلاص", "TotalQuantity": 330, "TotalRevenue": 14850}, {"ItemName": "مكي<PERSON> هواء", "TotalQuantity": 330, "TotalRevenue": 594000}], "analysis": "البيانات تشير إلى مبيعات عالية للمنتجات مثل 'مكيف هواء' و'قرص صلب1TB'. هناك حاجة لتحليل أعمق للاتجاهات الموسمية وتحسين استراتيجيات المبيعات.", "timestamp": "2025-07-27T18:24:33.543Z", "expiresAt": "2025-07-27T19:24:33.543Z", "executionTime": 4162, "dataHash": "3c22683de9cac3872cac53719a40d414"}, {"id": "d32addd41b6ac484e1a1a67956881795", "query": "تفاصيل مبيعات طاول جانبية ", "sql": "SELECT TOP 10 tbltemp_ItemsMain.ItemName, SUM(tbltemp_ItemsMain.Quantity) AS TotalQuantity, SUM(tbltemp_ItemsMain.Amount) AS TotalAmount FROM tbltemp_ItemsMain WHERE tbltemp_ItemsMain.ItemName = 'طاول جانبية' AND tbltemp_ItemsMain.DocumentName = 'فاتورة مبيعات' GROUP BY tbltemp_ItemsMain.ItemName ORDER BY TotalQuantity DESC", "data": [], "analysis": "لا يمكن إجراء تحليل مفصل بسبب عدم وجود بيانات. يُنصح بإضافة بيانات فعلية لتحليل الاتجاهات والأنماط.", "timestamp": "2025-07-27T18:36:18.611Z", "expiresAt": "2025-07-27T19:36:18.611Z", "executionTime": 3743, "dataHash": "d751713988987e9331980363e24189ce"}, {"id": "8db2362fb2d9de2afb7518529cf9f462", "query": "اكثر العملاء شراء ", "sql": "SELECT TOP 10 tbltemp_ItemsMain.ClientName, SUM(tbltemp_ItemsMain.Amount) AS TotalSpent FROM tbltemp_ItemsMain WHERE tbltemp_ItemsMain.DocumentName = 'فاتورة مبيعات' GROUP BY tbltemp_ItemsMain.ClientName ORDER BY TotalSpent DESC", "data": [{"ClientName": "سمر فهد", "TotalSpent": 384265}, {"ClientName": "عباس يوسف", "TotalSpent": 341275}, {"ClientName": "سليمان صالح", "TotalSpent": 314105}, {"ClientName": "جمي<PERSON>ة خليل", "TotalSpent": 299670}, {"ClientName": "عبير زاهر", "TotalSpent": 289440}, {"ClientName": "ماج<PERSON> سعيد", "TotalSpent": 260965}, {"ClientName": "ريم سلطان", "TotalSpent": 256780}, {"ClientName": "علي عبدالله", "TotalSpent": 250285}, {"ClientName": "منيرة عبد الله", "TotalSpent": 249105}, {"ClientName": "<PERSON><PERSON><PERSON><PERSON> محمد", "TotalSpent": 235920}], "analysis": "البيانات تشير إلى أن هناك تفاوتًا كبيرًا في الإنفاق بين العملاء، حيث يتراوح الإنفاق بين 235920 و 384265. هذا يشير إلى وجود عملاء ذوي قيمة عالية يمكن التركيز عليهم لزيادة المبيعات.", "timestamp": "2025-07-27T18:36:31.792Z", "expiresAt": "2025-07-27T19:36:31.792Z", "executionTime": 3487, "dataHash": "e3071c0d32b19360e7bf1eb0d08167b2"}, {"id": "796eea0a872a543695db23fe3a0f9c14", "query": "مشتريات العميلة جميلة خليل ", "sql": "SELECT TOP 10 tbltemp_ItemsMain.ClientName, SUM(tbltemp_ItemsMain.Amount) AS TotalSpent FROM tbltemp_ItemsMain WHERE tbltemp_ItemsMain.DocumentName = 'فاتورة مشتريات' AND tbltemp_ItemsMain.ClientName = 'جميلة خليل' GROUP BY tbltemp_ItemsMain.ClientName ORDER BY TotalSpent DESC", "data": [], "analysis": "تحليل الاستعلام يشير إلى رغبة في الحصول على معلومات حول مشتريات العميلة جميلة خليل. ومع ذلك، لا توجد بيانات متاحة للتحليل.", "timestamp": "2025-07-27T18:36:48.916Z", "expiresAt": "2025-07-27T19:36:48.916Z", "executionTime": 3057, "dataHash": "d751713988987e9331980363e24189ce"}], "version": "1.0.0", "lastCleanup": "2025-07-27T18:14:53.345Z"}