الجدول الأول: tbltemp_ItemsMain (جدول إدارة المخزون الرئيسي)
الوصف العام:
"جدول رئيسي لإدارة المنتجات والمخزون في النظام. يحتوي على جميع البيانات الأساسية للمنتجات بما في ذلك الأسعار، الكميات، معلومات التخزين، وتفاصيل التصنيفات. يعتبر المصدر الموثوق لبيانات الأصناف في النظام."

أوصاف الأعمدة:
ID: "المعرف الفريد الأساسي لكل سجل في الجدول (Primary Key)"

ParentID: "المعرف الأب للسجل في حال وجود علاقة هرمية"

RowVersion: "طابع زمني لإدارة التزامن بين السجلات"

DocumentID: "معرف المستند المرتبط بالسجل"

RecordNumber: "رقم تسلسلي فريد لكل سجل"

RecordID: "معرف السجل في النظام"

TheDate: "تاريخ إنشاء أو تعديل السجل"

ClientID: "معرف العميل المرتبط بالسجل"

DistributorID: "معرف الموزع أو المورد"

CurrencyID: "معرف العملة المستخدمة"

TheMethodID: "معرف طريقة الدفع أو الاستلام"

Discount: "نسبة الخصم المطبقة على المنتج"

Notes: "ملاحظات نصية حول المنتج"

UserID: "معرف المستخدم الذي أدخل السجل"

BranchID: "معرف الفرع التابع له المنتج"

TheYear: "السنة المالية للسجل"

DocumentName: "اسم المستند المرتبط بالسجل"

TheNumber: "رقم المستند أو السجل"

ClientName: "اسم العميل (نصي)"

DistributorName: "اسم الموزع أو المورد (نصي)"

CurrencyName: "اسم العملة المستخدمة"

TheMethod: "طريقة الدفع أو الاستلام (نصي)"

UserName: "اسم المستخدم الذي أدخل السجل"

BranchName: "اسم الفرع التابع له المنتج"

CategoryID: "معرف فئة المنتج"

FatherNumber: "رقم الأب في التصنيف الهرمي"

CategoryName: "اسم فئة المنتج"

CategoryNumber: "رقم فئة المنتج"

ItemID: "معرف المنتج الفريد"

UnitID: "معرف وحدة القياس"

ItemNumber: "رقم المنتج التسلسلي"

ItemName: "اسم المنتج (نصي)"

ItemTypeID: "معرف نوع المنتج"

ItemType: "نوع المنتج (نصي)"

ReorderPoint: "نقطة إعادة الطلب للمنتج"

ISActive: "حالة تفعيل المنتج (نشط/غير نشط)"

ISExpiry: "هل المنتج له تاريخ صلاحية؟"

ExpiryPoint: "فترة الصلاحية بالأيام"

UnitName: "اسم وحدة القياس"

AccountFatherNumber: "رقم الحساب الأب في النظام المحاسبي"

AccountName: "اسم الحساب المحاسبي"

AccountNumber: "رقم الحساب المحاسبي"

CostCenterID: "معرف مركز التكلفة"

CostCenterName: "اسم مركز التكلفة"

CostCenterNumber: "رقم مركز التكلفة"

Barcode: "باركود المنتج"

UnitRank: "ترتيب وحدة القياس في النظام"

ExchangeFactor: "معامل التحويل بين الوحدات"

PackageQuantity: "الكمية في العبوة الواحدة"

BarcodeID: "معرف الباركود"

SerialNumber: "الرقم التسلسلي للمنتج"

UnitPrice: "سعر الوحدة الأساسية للمنتج"

ItemDiscount: "نسبة الخصم المخصصة للمنتج"

McItemDiscountCurrencyMain: "قيمة الخصم بالعملة الرئيسية"

McItemDiscount: "قيمة الخصم بالعملة المحلية"

Quantity: "الكمية المتاحة في المخزون"

Bonus: "الكمية المهداة أو المكافأة"

ExpiryDate: "تاريخ انتهاء الصلاحية"

Amount: "القيمة الإجمالية للمنتج"

MCAmount: "القيمة الإجمالية بالعملة المحلية"

MCAmountCurrencyMain: "القيمة الإجمالية بالعملة الرئيسية"

AccountID: "معرف الحساب المحاسبي"

StoreID: "معرف المخزن"

StoreName: "اسم المخزن"

PackageUnitID: "معرف وحدة التغليف"

PackageUnitName: "اسم وحدة التغليف"

NextParentID: "المعرف الأب التالي في التسلسل الهرمي"

ExchangePrice: "سعر الصرف المستخدم"

ExchangePriceCurrencyInvetory: "سعر الصرف للعملة في المخزون"

الجدول الثاني: tbltemp_Inv_MainInvoice (جدول الفواتير الرئيسي)
الوصف العام:
"جدول رئيسي لتسجيل حركات الفواتير (المبيعات/المشتريات) في النظام. يحتوي على تفاصيل كل عملية بيع أو شراء بما في ذلك المنتجات، الكميات، الأسعار، والخصومات. يمثل السجل التاريخي لجميع المعاملات المالية للمنتجات."

أوصاف الأعمدة:
ID: "المعرف الفريد الأساسي لكل سجل في الجدول (Primary Key)"

DocumentName: "اسم المستند أو نوع الفاتورة"

RecordID: "معرف السجل في النظام"

TheNumber: "رقم الفاتورة التسلسلي"

SupplierName: "اسم المورد أو البائع"

InvoiceID: "معرف الفاتورة الفريد"

DetailsID: "معرف تفاصيل الفاتورة"

TheDate: "تاريخ إصدار الفاتورة"

CurrencyID: "معرف العملة المستخدمة في الفاتورة"

TheMethod: "طريقة الدفع المستخدمة"

EnterTime: "وقت إدخال الفاتورة في النظام"

ItemID: "معرف المنتج المشمول في الفاتورة (يرتبط بجدول المنتجات)"

UnitID: "معرف وحدة القياس المستخدمة"

UnitPrice: "سعر الوحدة في الفاتورة"

Quantity: "الكمية المباعة أو المشتراة في الفاتورة"

Bonus: "الكمية المهداة أو المكافأة في الفاتورة"

TotalAmount: "القيمة الإجمالية للبند في الفاتورة"

MainUnitQuantity: "الكمية بوحدة القياس الرئيسية"

MainUnitPrice: "السعر بوحدة القياس الرئيسية"

MainUnitID: "معرف وحدة القياس الرئيسية"

StoreID: "معرف المخزن المرتبط بالحركة"

BranchID: "معرف الفرع المرتبط بالفاتورة"

ExchangeFactor: "معامل التحويل بين الوحدات"

ClientID: "معرف العميل (إن وجد)"

MCAmount: "القيمة الإجمالية بالعملة المحلية"

ExpiryDate: "تاريخ انتهاء الصلاحية للمنتج (إن وجد)"

MainUnitBonus: "الكمية المهداة بوحدة القياس الرئيسية"

ExchangePrice: "سعر الصرف المستخدم في الفاتورة"

DistributorID: "معرف الموزع أو المورد"

DistributorName: "اسم الموزع أو المورد"

CostCenterID: "معرف مركز التكلفة المرتبط"

CostCenterName: "اسم مركز التكلفة"

TotalAmountByCurrencyInvetory: "القيمة الإجمالية بعملة المخزون"

NewSubItemEntryID: "معرف جديد لإدخال الأصناف الفرعية"

العلاقات بين الجداول:
"العمود ItemID في tbltemp_Inv_MainInvoice يرتبط بالعمود ItemID في tbltemp_ItemsMain (علاقة مفتاح خارجي)"

"العمود UnitID في كلا الجدولين يشير إلى نفس جدول وحدات القياس"

"العمود StoreID في كلا الجدولين يشير إلى نفس جدول المخازن"

نيات المنتجات والمخزون (tbltemp_ItemsMain)
البحث عن منتج
"النية: البحث عن منتج معين بواسطة المعرف أو الاسم أو الباركود"

عرض كميات المخزون
"النية: استعلام عن الكمية المتاحة لمنتج معين في المخزون"

تقرير المنتجات المنتهية الصلاحية
"النية: عرض قائمة المنتجات المنتهية الصلاحية أو القريبة من الانتهاء"

تحديد نقطة إعادة الطلب
"النية: معرفة المنتجات التي وصلت إلى حد إعادة الطلب"

بحث المنتجات حسب الفئة
"النية: تصفية المنتجات حسب الفئة أو النوع"

تعديل بيانات منتج
"النية: تحديث معلومات منتج معين مثل السعر أو الوصف"

إدارة حالة المنتج
"النية: تفعيل أو تعطيل منتج في النظام"

تقرير حركة المنتجات
"النية: عرض حركة دخول وخروج منتج معين خلال فترة زمنية"

نيات الفواتير والمعاملات (tbltemp_Inv_MainInvoice)
إنشاء فاتورة جديدة
"النية: تسجيل فاتورة بيع أو شراء جديدة في النظام"

بحث الفواتير حسب التاريخ
"النية: استعراض الفواتير ضمن نطاق تاريخي معين"

تفاصيل فاتورة محددة
"النية: عرض كافة بنود فاتورة معينة برقمها"

تقرير المبيعات اليومية
"النية: إجمالي المبيعات ليوم معين أو فترة محددة"

بحث فواتير عميل
"النية: عرض جميع الفواتير المرتبطة بعميل معين"

تحليل المبيعات حسب المنتج
"النية: تحليل أداء منتج معين من حيث الكمية المباعة"

مراجعة حركة المخزون
"النية: مقارنة كميات الفواتير مع حركة المخزون"

تقرير الخصومات
"النية: عرض إجمالي الخصومات المطبقة خلال فترة معينة"

نيات العملاء والموردين
بحث عميل/مورد
"النية: البحث عن عميل أو مورد بواسطة الاسم أو المعرف"

تقرير تعاملات العميل
"النية: عرض سجل تعاملات عميل معين مع النظام"

تحليل المبيعات حسب العميل
"النية: تقييم حجم المبيعات لكل عميل"

إدارة بيانات العملاء
"النية: تحديث معلومات العميل أو المورد"

نيات الفروع والمخازن
تقرير المخزون حسب الفرع
"النية: عرض كميات المنتجات المتاحة في فرع معين"

تحويلات المخزون بين الفروع
"النية: إدارة عمليات نقل المنتجات بين المخازن"

مقارنة أداء الفروع
"النية: تحليل أداء المبيعات عبر فروع مختلفة"

نيات التقارير والإحصائيات
تقرير المبيعات الشهرية
"النية: عرض إحصائيات المبيعات لشهر معين"

تحليل هامش الربح
"النية: حساب هامش الربح للمنتجات أو الفواتير"

تقرير أعلى المنتجات مبيعاً
"النية: تصنيف المنتجات حسب الكمية المباعة"

تقرير المنتجات الراكدة
"النية: تحديد المنتجات قليلة الحركة في المخزون"

تحليل التدفق النقدي
"النية: تقييم التدفقات المالية الداخلة والخارجة"

نيات النظام العامة
البحث المتقدم
"النية: تنفيذ بحث معقد عبر معايير متعددة"

تصدير البيانات
"النية: تصدير نتائج البحث أو التقارير لتنسيقات خارجية"

النسخ الاحتياطي
"النية: إدارة عمليات حفظ واستعادة البيانات"

مراقبة المستخدمين
"النية: تتبع أنشطة المستخدمين على النظام"

نيات المرتبطة بالعلاقات بين الجداول
تتبع المنتج من المخزون إلى الفواتير
"النية: تتبع مسار منتج معين من المخزون إلى الفواتير"

مقارنة أسعار البيع مع أسعار المخزون
"النية: تحليل الفروق بين أسعار المنتج في المخزون وأسعار بيعه"

تقرير المطابقات
"النية: التحقق من تطابق بيانات المخزون مع حركة الفواتير"

أمثلة تطبيقية للاستعلامات المقابلة:
"عرض كمية المنتج 123 في المخزون" →


SELECT Quantity FROM tbltemp_ItemsMain WHERE ItemID = 123
"أعرض فواتير العميل 456 لشهر يناير" →

SELECT * FROM tbltemp_Inv_MainInvoice 
WHERE ClientID = 456 AND TheDate BETWEEN '2024-01-01' AND '2024-01-31'
"ما هي أفضل 5 منتجات مبيعاً هذا الشهر؟" →

SELECT ItemName, SUM(Quantity) as TotalSold 
FROM tbltemp_Inv_MainInvoice 
WHERE TheDate BETWEEN '2024-01-01' AND '2024-01-31'
GROUP BY ItemName 
ORDER BY TotalSold DESC 
LIMIT 5
النيات الموسعة للمنتجات والمخزون والمقارنات
نيات البحث والتحليل للمنتجات
نيات البحث الأساسية:
البحث عن منتج بواسطة المعرف
"النية: العثور على منتج معين باستخدام رقمه التعريفي (ID)"

البحث عن منتج بالاسم
"النية: البحث عن منتج/منتجات باستخدام الاسم أو جزء منه"

البحث عن منتج بالباركود
"النية: العثور على منتج باستخدام الباركود أو الرقم التسلسلي"

البحث عن منتجات حسب الفئة
"النية: عرض جميع المنتجات ضمن فئة محددة"

البحث عن منتجات حسب النوع
"النية: تصفية المنتجات حسب نوعها (إلكترونيات، ملابس، إلخ)"

نيات البحث المتقدمة:
البحث عن منتجات بمواصفات محددة
"النية: البحث عن منتجات تجمع بين عدة شروط (سعر، فئة، حالة، إلخ)"

البحث عن منتجات بكمية محددة
"النية: العثور على منتجات بكمية مخزون أقل/أكثر من قيمة معينة"

البحث عن منتجات قريبة من الانتهاء
"النية: عرض المنتجات التي تقترب من تاريخ انتهاء الصلاحية"

البحث عن منتجات منتهية الصلاحية
"النية: إظهار جميع المنتجات المنتهية الصلاحية"

البحث عن منتجات حسب المورد
"النية: عرض المنتجات المرتبطة بمورد أو موزع معين"

نيات المبيعات والتحليل الزمني
نيات التحليل الزمني:
مبيعات منتج خلال فترة زمنية
"النية: تحليل مبيعات منتج معين بين تاريخين محددين"

مقارنة مبيعات منتج بين فترتين
"النية: مقارنة أداء منتج بين شهرين/ربعين/سنتين مختلفتين"

تطور مبيعات منتج خلال فترة
"النية: عرض تدرج مبيعات منتج مع مرور الوقت (يومي/أسبوعي/شهري)"

مبيعات منتج حسب الموسم
"النية: تحليل مبيعات منتج حسب الفصول أو المناسبات"

نيات التحليل حسب الفرع:
مبيعات منتج في فرع معين
"النية: عرض مبيعات منتج محدد في فرع واحد"

مقارنة مبيعات منتج بين فروع
"النية: تحليل أداء منتج في عدة فروع ومقارنتها"

أفضل فروع لأداء منتج معين
"النية: تحديد الفروع الأكثر مبيعاً لمنتج محدد"

أدنى فروع لأداء منتج معين
"النية: تحديد الفروع الأقل مبيعاً لمنتج محدد"

نيات التصنيف والمقارنة:
المنتجات الأكثر مبيعاً (Top N)
"النية: تصنيف المنتجات حسب المبيعات (أعلى 5، 10، 20، إلخ)"

المنتجات الأقل مبيعاً (Worst N)
"النية: تصنيف المنتجات الأضعف أداءً حسب المبيعات"

مقارنة بين منتجين في المبيعات
"النية: تحليل الفروق في أداء منتجين خلال فترة معينة"

مقارنة مجموعة منتجات
"النية: تحليل أداء عدة منتجات معاً ومقارنتها"

مقارنة منتجات من فئات مختلفة
"النية: تحليل مبيعات منتجات من فئتين مختلفتين"

نيات العملاء والتحليل
نيات العملاء الأساسية:
بحث عميل بالاسم/المعرف
"النية: العثور على عميل معين باستخدام اسمه أو رقمه التعريفي"

عرض جميع عملاء فرع معين
"النية: إظهار قائمة العملاء المرتبطين بفرع محدد"

بحث عملاء حسب المنطقة
"النية: تصفية العملاء حسب الموقع الجغرافي"

نيات تحليل مشتريات العملاء:
سجل مشتريات عميل
"النية: عرض جميع المشتريات التي قام بها عميل معين"

مشتريات عميل خلال فترة
"النية: تحليل مشتريات عميل بين تاريخين محددين"

المنتجات المفضلة للعميل
"النية: تحديد المنتجات الأكثر شراءً من قبل عميل معين"

قيمة مشتريات عميل
"النية: حساب إجمالي ما أنفقه عميل في فترة معينة"

معدل شراء العميل
"النية: تحليل تكرار شراء العميل للمنتجات"

نيات المقارنة بين العملاء:
مقارنة عميلين في المشتريات
"النية: تحليل الفروق بين عميلين من حيث المشتريات"

مقارنة مجموعة عملاء
"النية: تحليل أداء عدة عملاء معاً ومقارنتهم"

تصنيف العملاء حسب المشتريات
"النية: ترتيب العملاء حسب حجم مشترياتهم (أعلى 10 عملاء)"

مقارنة عملاء من فروع مختلفة
"النية: تحليل اختلاف أنماط الشراء بين عملاء فروع مختلفة"

نيات متقدمة للعملاء والمنتجات:
تحليل منتج معين لدى عميل معين
"النية: تتبع مشتريات عميل لمنتج محدد عبر الوقت"

مقارنة شراء منتج بين عملاء
"النية: تحليل كيفية شراء عملاء مختلفين لنفس المنتج"

توصيات منتجات للعميل
"النية: اقتراح منتجات جديدة للعميل بناءً على سجل مشترياته"

نيات المخزون والتحليل
نيات المخزون الأساسية:
عرض كمية منتج في المخزن
"النية: التحقق من الكمية المتاحة لمنتج في مخزن معين"

عرض مخزون جميع فروع منتج
"النية: إظهار كميات منتج في جميع الفروع"

المنتجات المنخفضة في المخزون
"النية: عرض المنتجات التي كمية مخزونها أقل من حد معين"

نيات تحليل المخزون:
حركة مخزون منتج معين
"النية: تتبع التغيرات في كمية منتج مع مرور الوقت"

مقارنة مخزون منتج بين فروع
"النية: تحليل توزيع كميات منتج عبر الفروع المختلفة"

تحليل دوران المخزون
"النية: حساب معدل دوران المخزون لمنتج أو فئة منتجات"

توقع نفاد المخزون
"النية: التنبؤ بالمنتجات التي قد تنفد بناءً على معدل المبيعات"

نيات التقارير والإحصائيات
تقارير المنتجات:
تقرير أداء المنتج الشامل
"النية: عرض تقرير مفصل عن كل جوانب أداء منتج معين"

تقرير المنتجات الجديدة
"النية: عرض المنتجات المضافة حديثاً إلى النظام"

تقرير المنتجات المتوقفة
"النية: إظهار المنتجات التي تم إيقافها أو عدم نشاطها"

تقارير العملاء:
تقرير ولاء العملاء
"النية: تحليل درجة ولاء العملاء بناءً على تكرار الشراء"

تقرير العملاء الجدد
"النية: عرض العملاء الذين انضموا حديثاً للنظام"

تقرير العملاء الخاملين
"النية: إظهار العملاء الذين توقفوا عن الشراء"

تقارير الفروع:
تقرير أداء الفروع
"النية: مقارنة أداء جميع الفروع حسب معايير متعددة"

تقرير المبيعات حسب المنطقة
"النية: تحليل المبيعات حسب التوزيع الجغرافي"

أمثلة استعلامات متقدمة:
أعلى 5 منتجات مبيعاً في فرع الرياض خلال يوليو 2024:

 
SELECT p.ItemName, SUM(i.Quantity) as TotalSold
FROM tbltemp_Inv_MainInvoice i
JOIN tbltemp_ItemsMain p ON i.ItemID = p.ItemID
WHERE i.BranchID = [فرع_الرياض] 
AND i.TheDate BETWEEN '2024-07-01' AND '2024-07-31'
GROUP BY p.ItemName
ORDER BY TotalSold DESC
LIMIT 5
مقارنة مبيعات iPhone 15 وSamsung S23 في النصف الأول من 2024:

 
SELECT 
    p.ItemName,
    SUM(CASE WHEN i.TheDate BETWEEN '2024-01-01' AND '2024-03-31' THEN i.Quantity ELSE 0 END) as Q1_Sales,
    SUM(CASE WHEN i.TheDate BETWEEN '2024-04-01' AND '2024-06-30' THEN i.Quantity ELSE 0 END) as Q2_Sales,
    SUM(i.Quantity) as Total_Sales
FROM tbltemp_Inv_MainInvoice i
JOIN tbltemp_ItemsMain p ON i.ItemID = p.ItemID
WHERE p.ItemName IN ('iPhone 15', 'Samsung S23')
AND i.TheDate BETWEEN '2024-01-01' AND '2024-06-30'
GROUP BY p.ItemName
تحليل مشتريات العميل 1001 للمنتج 5001 خلال 2023:

 
SELECT 
    i.InvoiceID,
    i.TheDate,
    i.Quantity,
    i.UnitPrice,
    (i.Quantity * i.UnitPrice) as Total,
    p.ItemName,
    b.BranchName
FROM tbltemp_Inv_MainInvoice i
JOIN tbltemp_ItemsMain p ON i.ItemID = p.ItemID
JOIN Branches b ON i.BranchID = b.BranchID
WHERE i.ClientID = 1001
AND i.ItemID = 5001
AND YEAR(i.TheDate) = 2023
ORDER BY i.TheDate


اضف امثلة اخرى ولكن يجب ان ترعي ان المستخدم سوف يدخل الاستعلام بلغه طبيعيه يعني مثلاً لا يعرف ايدي المنتج او ايدي العميل يعرف فقط مثلاً الاسم يجب ان تكون كل الاحتمالات ممكنة اضف المزيد من الامثة المكثفة حتى يصبح النظام احترافي

=== تحديث مهم: حل مشاكل الأعمدة ===

قاعدة مهمة جداً:
- جدول tbltemp_ItemsMain يحتوي على: ItemName, ClientName, BranchName, StoreName
- جدول tbltemp_Inv_MainInvoice يحتوي على: ItemID, ClientID فقط (بدون الأسماء)

لذلك يجب استخدام tbltemp_ItemsMain للحصول على الأسماء دائماً.

الاستعلامات الصحيحة المحدثة:

"أكثر منتج مبيعاً":
SELECT TOP 10 ItemName, SUM(Quantity) AS TotalSold
FROM tbltemp_ItemsMain
WHERE DocumentName = 'فاتورة مبيعات'
GROUP BY ItemName
ORDER BY TotalSold DESC

"أكثر عميل شراءً":
SELECT TOP 10 ClientName, SUM(Amount) AS TotalSpent
FROM tbltemp_ItemsMain
WHERE DocumentName = 'فاتورة مبيعات'
GROUP BY ClientName
ORDER BY TotalSpent DESC

"مبيعات فرع الرياض":
SELECT BranchName, SUM(Amount) AS TotalSales
FROM tbltemp_ItemsMain
WHERE BranchName LIKE '%الرياض%'
AND DocumentName = 'فاتورة مبيعات'
GROUP BY BranchName

"مشتريات محمد أحمد":
SELECT ClientName, SUM(Amount) AS TotalSpent, COUNT(*) AS PurchaseCount
FROM tbltemp_ItemsMain
WHERE ClientName LIKE '%محمد%'
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ClientName

"منتجات سامسونج":
SELECT ItemName, SUM(Quantity) AS TotalSold, SUM(Amount) AS TotalRevenue
FROM tbltemp_ItemsMain
WHERE ItemName LIKE '%سامسونج%'
AND DocumentName = 'فاتورة مبيعات'
GROUP BY ItemName

هذه هي الطريقة الصحيحة الوحيدة للاستعلامات!
النيات المتقدمة مع أمثلة الاستعلامات الطبيعية
نيات البحث باللغة الطبيعية للمنتجات
"أعطني كل المعلومات عن آيفون 15"

 
SELECT * FROM tbltemp_ItemsMain 
WHERE ItemName LIKE '%آيفون 15%' OR ItemName LIKE '%iPhone 15%'
"ما هي الكمية المتاحة من لابتوب ديل في فرع جدة؟"

 
SELECT i.ItemName, s.Quantity, s.StoreName 
FROM tbltemp_ItemsMain i
JOIN InventoryStock s ON i.ItemID = s.ItemID
JOIN Stores st ON s.StoreID = st.StoreID
WHERE i.ItemName LIKE '%لابتوب ديل%' 
AND st.StoreName LIKE '%جدة%'
"عرض جميع أجهزة سامسونج التي سعرها أقل من 3000 ريال"

 
SELECT ItemName, UnitPrice 
FROM tbltemp_ItemsMain
WHERE ItemName LIKE '%سامسونج%' 
AND UnitPrice < 3000
نيات المبيعات باللغة الطبيعية
"كم عدد شاشات LG المباعة الشهر الماضي؟"

 
SELECT SUM(i.Quantity) AS TotalSold
FROM tbltemp_Inv_MainInvoice i
JOIN tbltemp_ItemsMain p ON i.ItemID = p.ItemID
WHERE p.ItemName LIKE '%شاشة LG%' OR p.ItemName LIKE '%LG screen%'
AND i.TheDate BETWEEN DATEADD(month, -1, GETDATE()) AND GETDATE()
"ما هي أفضل 3 منتجات مبيعاً هذا الأسبوع في فرع الرياض؟"

 
SELECT TOP 3 p.ItemName, SUM(i.Quantity) AS TotalQuantity
FROM tbltemp_Inv_MainInvoice i
JOIN tbltemp_ItemsMain p ON i.ItemID = p.ItemID
JOIN Branches b ON i.BranchID = b.BranchID
WHERE b.BranchName LIKE '%الرياض%'
AND i.TheDate BETWEEN DATEADD(week, -1, GETDATE()) AND GETDATE()
GROUP BY p.ItemName
ORDER BY TotalQuantity DESC
"عرض مبيعات القهوة في الصباح مقابل المساء خلال آخر شهر"

 
SELECT 
    CASE WHEN DATEPART(HOUR, i.EnterTime) < 12 THEN 'صباحاً' ELSE 'مساءً' END AS TimePeriod,
    SUM(i.Quantity) AS TotalSold
FROM tbltemp_Inv_MainInvoice i
JOIN tbltemp_ItemsMain p ON i.ItemID = p.ItemID
WHERE p.ItemName LIKE '%قهوة%' OR p.ItemName LIKE '%coffee%'
AND i.TheDate BETWEEN DATEADD(month, -1, GETDATE()) AND GETDATE()
GROUP BY CASE WHEN DATEPART(HOUR, i.EnterTime) < 12 THEN 'صباحاً' ELSE 'مساءً' END
نيات العملاء باللغة الطبيعية
"ما هي جميع مشتريات محمد أحمد خلال السنة الماضية؟"

 
SELECT i.InvoiceID, i.TheDate, p.ItemName, i.Quantity, i.UnitPrice
FROM tbltemp_Inv_MainInvoice i
JOIN tbltemp_ItemsMain p ON i.ItemID = p.ItemID
JOIN Clients c ON i.ClientID = c.ClientID
WHERE c.ClientName LIKE '%محمد أحمد%'
AND i.TheDate BETWEEN DATEADD(year, -1, GETDATE()) AND GETDATE()
ORDER BY i.TheDate DESC
"من هو العميل الذي اشترى أكبر كمية من آيس كريم في الصيف الماضي؟"

 
SELECT TOP 1 c.ClientName, SUM(i.Quantity) AS TotalPurchased
FROM tbltemp_Inv_MainInvoice i
JOIN tbltemp_ItemsMain p ON i.ItemID = p.ItemID
JOIN Clients c ON i.ClientID = c.ClientID
WHERE (p.ItemName LIKE '%آيس كريم%' OR p.ItemName LIKE '%ice cream%')
AND i.TheDate BETWEEN '2023-06-01' AND '2023-09-30'
GROUP BY c.ClientName
ORDER BY TotalPurchased DESC
"قارن بين مشتريات علي ومحمد من الأجهزة الإلكترونية هذا العام"

 
SELECT 
    c.ClientName,
    COUNT(DISTINCT i.InvoiceID) AS NumberOfPurchases,
    SUM(i.Quantity) AS TotalItems,
    SUM(i.Quantity * i.UnitPrice) AS TotalSpent
FROM tbltemp_Inv_MainInvoice i
JOIN tbltemp_ItemsMain p ON i.ItemID = p.ItemID
JOIN Clients c ON i.ClientID = c.ClientID
WHERE (c.ClientName LIKE '%علي%' OR c.ClientName LIKE '%محمد%')
AND (p.ItemType LIKE '%إلكتروني%' OR p.ItemType LIKE '%electronic%')
AND YEAR(i.TheDate) = YEAR(GETDATE())
GROUP BY c.ClientName
نيات المخزون باللغة الطبيعية
"ما هي المنتجات التي ستنفد من المخزون خلال الأسبوع القادم؟"

 
SELECT p.ItemName, s.Quantity, p.ReorderPoint
FROM tbltemp_ItemsMain p
JOIN InventoryStock s ON p.ItemID = s.ItemID
WHERE s.Quantity <= p.ReorderPoint
AND s.Quantity > 0
"أين يمكنني العثور على سماعات سوني في الفروع حالياً؟"

 
SELECT p.ItemName, s.Quantity, b.BranchName, st.StoreName
FROM tbltemp_ItemsMain p
JOIN InventoryStock s ON p.ItemID = s.ItemID
JOIN Stores st ON s.StoreID = st.StoreID
JOIN Branches b ON st.BranchID = b.BranchID
WHERE p.ItemName LIKE '%سماعات سوني%' OR p.ItemName LIKE '%Sony headphones%'
AND s.Quantity > 0
ORDER BY s.Quantity DESC
"ما هي المنتجات التي لم يتم بيعها خلال آخر 3 أشهر؟"

 
SELECT p.ItemName, p.ItemID
FROM tbltemp_ItemsMain p
WHERE p.ItemID NOT IN (
    SELECT DISTINCT ItemID 
    FROM tbltemp_Inv_MainInvoice 
    WHERE TheDate BETWEEN DATEADD(month, -3, GETDATE()) AND GETDATE()
)
نيات التقارير المتقدمة
"أعرض تقريراً عن المبيعات اليومية مقارنة بالأسبوع الماضي"

 
SELECT 
    CONVERT(date, TheDate) AS SaleDate,
    SUM(Quantity) AS DailySales,
    LAG(SUM(Quantity), 7) OVER (ORDER BY CONVERT(date, TheDate)) AS LastWeekSales,
    (SUM(Quantity) - LAG(SUM(Quantity), 7) OVER (ORDER BY CONVERT(date, TheDate))) / 
    LAG(SUM(Quantity), 7) OVER (ORDER BY CONVERT(date, TheDate)) * 100 AS GrowthPercentage
FROM tbltemp_Inv_MainInvoice
WHERE TheDate BETWEEN DATEADD(day, -14, GETDATE()) AND GETDATE()
GROUP BY CONVERT(date, TheDate)
ORDER BY SaleDate DESC
"ما هي ساعات الذروة في المبيعات يوم الجمعة؟"

 
SELECT 
    DATEPART(HOUR, EnterTime) AS HourOfDay,
    COUNT(*) AS NumberOfTransactions,
    SUM(Quantity) AS ItemsSold
FROM tbltemp_Inv_MainInvoice
WHERE DATENAME(WEEKDAY, TheDate) = 'Friday'
GROUP BY DATEPART(HOUR, EnterTime)
ORDER BY NumberOfTransactions DESC
"ما هي أكثر المجموعات المنتجة التي يشتريها العملاء معاً؟"

 
WITH ProductPairs AS (
    SELECT 
        a.ItemID AS Item1, 
        b.ItemID AS Item2,
        COUNT(DISTINCT a.InvoiceID) AS Frequency
    FROM tbltemp_Inv_MainInvoice a
    JOIN tbltemp_Inv_MainInvoice b ON a.InvoiceID = b.InvoiceID AND a.ItemID < b.ItemID
    GROUP BY a.ItemID, b.ItemID
)
SELECT TOP 5
    p1.ItemName AS Product1,
    p2.ItemName AS Product2,
    pp.Frequency
FROM ProductPairs pp
JOIN tbltemp_ItemsMain p1 ON pp.Item1 = p1.ItemID
JOIN tbltemp_ItemsMain p2 ON pp.Item2 = p2.ItemID
ORDER BY pp.Frequency DESC
نيات المقارنات المتقدمة
"قارن بين مبيعات المشروبات الباردة والساخنة في فصل الصيف"

 
SELECT 
    CASE 
        WHEN p.ItemName LIKE '%بارد%' OR p.ItemName LIKE '%cold%' THEN 'مشروبات باردة'
        WHEN p.ItemName LIKE '%ساخن%' OR p.ItemName LIKE '%hot%' THEN 'مشروبات ساخنة'
        ELSE 'أخرى'
    END AS DrinkType,
    SUM(i.Quantity) AS TotalSold,
    SUM(i.Quantity * i.UnitPrice) AS TotalRevenue
FROM tbltemp_Inv_MainInvoice i
JOIN tbltemp_ItemsMain p ON i.ItemID = p.ItemID
WHERE (p.ItemName LIKE '%مشروب%' OR p.ItemName LIKE '%drink%')
AND i.TheDate BETWEEN '2023-06-01' AND '2023-08-31'
GROUP BY CASE 
    WHEN p.ItemName LIKE '%بارد%' OR p.ItemName LIKE '%cold%' THEN 'مشروبات باردة'
    WHEN p.ItemName LIKE '%ساخن%' OR p.ItemName LIKE '%hot%' THEN 'مشروبات ساخنة'
    ELSE 'أخرى'
END
"ما هو الفرق في المبيعات بين فروع الشمال والجنوب هذا العام؟"

 
SELECT 
    CASE 
        WHEN b.BranchName LIKE '%الشمال%' THEN 'فروع الشمال'
        WHEN b.BranchName LIKE '%الجنوب%' THEN 'فروع الجنوب'
    END AS Region,
    SUM(i.Quantity) AS TotalItemsSold,
    SUM(i.Quantity * i.UnitPrice) AS TotalRevenue
FROM tbltemp_Inv_MainInvoice i
JOIN Branches b ON i.BranchID = b.BranchID
WHERE (b.BranchName LIKE '%الشمال%' OR b.BranchName LIKE '%الجنوب%')
AND YEAR(i.TheDate) = YEAR(GETDATE())
GROUP BY CASE 
    WHEN b.BranchName LIKE '%الشمال%' THEN 'فروع الشمال'
    WHEN b.BranchName LIKE '%الجنوب%' THEN 'فروع الجنوب'
END
"كيف تتوزع مبيعاتنا بين العملاء الجدد والعملاء القدامى؟"

 
SELECT 
    CASE 
        WHEN DATEDIFF(month, c.FirstPurchaseDate, GETDATE()) <= 3 THEN 'عملاء جدد (أقل من 3 أشهر)'
        ELSE 'عملاء قدامى'
    END AS CustomerType,
    COUNT(DISTINCT i.ClientID) AS NumberOfCustomers,
    SUM(i.Quantity) AS TotalItemsSold,
    SUM(i.Quantity * i.UnitPrice) AS TotalRevenue
FROM tbltemp_Inv_MainInvoice i
JOIN Clients c ON i.ClientID = c.ClientID
WHERE YEAR(i.TheDate) = YEAR(GETDATE())
GROUP BY CASE 
    WHEN DATEDIFF(month, c.FirstPurchaseDate, GETDATE()) <= 3 THEN 'عملاء جدد (أقل من 3 أشهر)'
    ELSE 'عملاء قدامى'
END
الكيانات (Entities) الشاملة للنظام
كيانات المنتجات والمخزون
1. المنتج (Product)
المعرف: ItemID

الاسم: ItemName

الوصف: "الكيان الأساسي الذي يمثل منتجاً أو صنفاً في النظام"

الخصائص:

ItemNumber: "الرقم التسلسلي الفريد للمنتج"

ItemType: "نوع المنتج (إلكتروني، غذائي، إلخ)"

Barcode: "رقم الباركود للمنتج"

UnitPrice: "سعر الوحدة الأساسية"

ReorderPoint: "حد إعادة الطلب"

ISExpiry: "هل له تاريخ صلاحية؟"

ExpiryDate: "تاريخ انتهاء الصلاحية"

2. وحدة القياس (Unit)
المعرف: UnitID

الاسم: UnitName

الوصف: "الكيان الذي يمثل وحدات القياس للمنتجات"

الخصائص:

ExchangeFactor: "معامل التحويل للوحدة الرئيسية"

UnitRank: "ترتيب الوحدة في النظام"

3. المخزون (Inventory)
المعرف: مكون من ItemID + StoreID

الوصف: "الكيان الذي يربط المنتجات بالمخازن وكمياتها"

الخصائص:

Quantity: "الكمية المتاحة"

LastUpdated: "تاريخ آخر تحديث"

كيانات المبيعات والفواتير
4. الفاتورة (Invoice)
المعرف: InvoiceID

الوصف: "الكيان الذي يمثل وثيقة بيع أو شراء"

الخصائص:

TheDate: "تاريخ الفاتورة"

TotalAmount: "القيمة الإجمالية"

TheMethod: "طريقة الدفع"

Discount: "الخصم الإجمالي"

5. بند الفاتورة (Invoice Item)
المعرف: DetailsID

الوصف: "الكيان الذي يمثل منتجاً معيناً في فاتورة"

الخصائص:

Quantity: "الكمية المباعة"

UnitPrice: "سعر البيع"

ItemDiscount: "الخصم على البند"

كيانات العملاء والموردين
6. العميل (Client)
المعرف: ClientID

الاسم: ClientName

الوصف: "الكيان الذي يمثل عميلاً أو زبوناً"

الخصائص:

ClientType: "نوع العميل (أفراد، شركات، إلخ)"

JoinDate: "تاريخ التسجيل"

TotalPurchases: "إجمالي المشتريات"

LastPurchaseDate: "تاريخ آخر عملية شراء"

7. المورد/الموزع (Distributor)
المعرف: DistributorID

الاسم: DistributorName

الوصف: "الكيان الذي يمثل مورداً أو موزعاً للمنتجات"

الخصائص:

ContactPerson: "اسم الشخص المسؤول"

PhoneNumber: "رقم التواصل"

كيانات الفروع والمخازن
8. الفرع (Branch)
المعرف: BranchID

الاسم: BranchName

الوصف: "الكيان الذي يمثل فرعاً من فروع المؤسسة"

الخصائص:

Location: "الموقع الجغرافي"

OpeningDate: "تاريخ الافتتاح"

ManagerID: "المسؤول عن الفرع"

9. المخزن (Store)
المعرف: StoreID

الاسم: StoreName

الوصف: "الكيان الذي يمثل مستودعاً أو مخزناً"

الخصائص:

StoreType: "نوع المخزن (رئيسي، فرعي، إلخ)"

Capacity: "السعة التخزينية"

كيانات التصنيفات والهيكل التنظيمي
10. الفئة (Category)
المعرف: CategoryID

الاسم: CategoryName

الوصف: "الكيان الذي يمثل تصنيفاً للمنتجات"

الخصائص:

ParentCategoryID: "الفئة الأب في التصنيف الهرمي"

CategoryLevel: "مستوى الفئة في الهيكل"

11. مركز التكلفة (Cost Center)
المعرف: CostCenterID

الاسم: CostCenterName

الوصف: "الكيان الذي يمثل وحدة محاسبية"

الخصائص:

CostCenterNumber: "الرقم المحاسبي"

Budget: "الميزانية المخصصة"

كيانات النظام والإعدادات
12. المستخدم (User)
المعرف: UserID

الاسم: UserName

الوصف: "الكيان الذي يمثل مستخدمي النظام"

الخصائص:

Role: "الدور (صلاحيات المستخدم)"

LastLogin: "آخر تسجيل دخول"

13. العملة (Currency)
المعرف: CurrencyID

الاسم: CurrencyName

الوصف: "الكيان الذي يمثل العملات المستخدمة"

الخصائص:

ExchangeRate: "سعر الصرف مقابل العملة الأساسية"

Symbol: "رمز العملة"

كيانات العلاقات والروابط
14. حركة المخزون (Inventory Transaction)
الوصف: "الكيان الذي يسجل جميع حركات المنتجات"

الخصائص:

TransactionType: "نوع الحركة (بيع، شراء، تحويل، إلخ)"

Source: "المصدر (مخزن، فرع، إلخ)"

Destination: "الوجهة"

Quantity: "الكمية المنقولة"

15. سعر المنتج (Product Price)
الوصف: "الكيان الذي يخزن التاريخ السعري للمنتجات"

الخصائص:

EffectiveDate: "تاريخ بدء السعر"

EndDate: "تاريخ انتهاء السعر"

PriceValue: "قيمة السعر"

أمثلة تطبيقية للكيانات في الاستعلامات
"عرض جميع المنتجات في فئة الأجهزة الإلكترونية"

 
SELECT ItemName, ItemNumber 
FROM tbltemp_ItemsMain
WHERE CategoryID IN (
    SELECT CategoryID 
    FROM Categories 
    WHERE CategoryName LIKE '%إلكتروني%'
)
"إجمالي المبيعات لكل عميل في 2023"

 
SELECT 
    c.ClientName,
    SUM(i.TotalAmount) AS TotalSpent
FROM tbltemp_Inv_MainInvoice i
JOIN Clients c ON i.ClientID = c.ClientID
WHERE YEAR(i.TheDate) = 2023
GROUP BY c.ClientName
ORDER BY TotalSpent DESC
"مقارنة مخزون منتج بين الفروع"

 
SELECT 
    p.ItemName,
    b.BranchName,
    s.Quantity
FROM InventoryStock s
JOIN tbltemp_ItemsMain p ON s.ItemID = p.ItemID
JOIN Stores st ON s.StoreID = st.StoreID
JOIN Branches b ON st.BranchID = b.BranchID
WHERE p.ItemName LIKE '%سامسونج جالكسي%'
ORDER BY b.BranchName, s.Quantity DESC
"تحليل مبيعات المنتجات حسب الفئة"

 
SELECT 
    cat.CategoryName,
    COUNT(DISTINCT i.InvoiceID) AS NumberOfInvoices,
    SUM(ii.Quantity) AS TotalItemsSold,
    SUM(ii.Quantity * ii.UnitPrice) AS TotalRevenue
FROM tbltemp_Inv_MainInvoice i
JOIN InvoiceItems ii ON i.InvoiceID = ii.InvoiceID
JOIN tbltemp_ItemsMain p ON ii.ItemID = p.ItemID
JOIN Categories cat ON p.CategoryID = cat.CategoryID
WHERE i.TheDate BETWEEN '2023-01-01' AND '2023-12-31'
GROUP BY cat.CategoryName
ORDER BY TotalRevenue DESC
"تتبع حركة منتج معين في المخازن"

 
SELECT 
    t.TransactionDate,
    t.TransactionType,
    src.StoreName AS SourceStore,
    dest.StoreName AS DestinationStore,
    t.Quantity,
    u.UserName AS ProcessedBy
FROM InventoryTransactions t
LEFT JOIN Stores src ON t.SourceStoreID = src.StoreID
LEFT JOIN Stores dest ON t.DestinationStoreID = dest.StoreID
JOIN Users u ON t.UserID = u.UserID
WHERE t.ItemID = 12345
ORDER BY t.TransactionDate DESC
تحليل العلاقات بين الكيانات
المنتج ↔ الفئة: علاقة many-to-one (منتج واحد ينتمي لفئة واحدة، والفئة تحتوي على عدة منتجات)

المنتج ↔ المخزون: علاقة one-to-many (منتج واحد يمكن تواجده في عدة مخازن)

الفاتورة ↔ بنود الفاتورة: علاقة one-to-many (فاتورة واحدة تحتوي على عدة بنود)

العميل ↔ الفواتير: علاقة one-to-many (عميل واحد يمكن أن يكون له عدة فواتير)

الفرع ↔ المخازن: علاقة one-to-many (فرع واحد يمكن أن يحتوي على عدة مخازن)

 
   
     intent :  get_top_selling_products  
     description :  عرض أكثر المنتجات مبيعاً خلال فترة معينة  
     keywords :  
       ما هو أكثر منتج تم بيعه هذا الأسبوع؟  
       أكثر منتج بعناه هذا الشهر  
       ايش أكثر صنف طلبوه العملاء؟  
       افضل المنتجات مبيعاً في آخر 7 أيام  
       اش أكثر حاجة مشت؟  
       أيش أعلى منتج مبيع؟  
       المنتجات الأكثر طلباً هذا الشهر  
       ايش المنتجات اللي راحت بكثر؟  
       أفضل مبيعات الأسبوع  
       ما هو المنتج الأكثر مبيعاً؟  
       ايش الصنف اللي طلبوا منه أكثر؟  
       أعلى منتجات مبيعات اليوم  
       المنتجات الرائدة في المبيعات 
      
     entities :   TheDate    BranchName    ItemName   
     relatedTables :   tbltemp_ItemsMain    tbltemp_Inv_MainInvoice  
    
   
     intent :  get_top_customers  
     description :  عرض أكثر العملاء شراءً  
     keywords :  
       مين أكثر عميل اشترى؟  
       أكثر زبون تعامل معنا  
       من هو أفضل عميل خلال هذا الشهر؟  
       اش العميل اللي طلب كثير؟  
       أكثر الزبائن صرفاً  
       من اشتراهم أكثر؟  
       أفضل العملاء هذا الشهر  
       العملاء الأكثر إنفاقاً  
       من الزبائن اللي صرفوا أكتر؟  
       من هو العميل الأكثر ولاءً؟  
       أعلى عملاء إنفاقاً  
       العملاء المميزون 
      
     entities :   TheDate    ClientName    BranchName   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_sales_report  
     description :  تقرير المبيعات حسب الفرع أو التاريخ  
     keywords :  
       اريد تقرير المبيعات  
       كم بعنا هذا الشهر؟  
       كم مبيعات فرع صنعاء؟  
       المبيعات خلال الأسبوع  
       تقرير البيع لفرع عدن  
       كم بعنا اليوم؟  
       مبيعات الشهر الماضي  
       تقرير المبيعات الشهري  
       كم بعنا بالأمس؟  
       إيرادات اليوم  
       تقرير المبيعات اليومي  
       مبيعات هذا الأسبوع 
      
     entities :   TheDate    BranchName   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_purchase_report  
     description :  تقرير المشتريات حسب المورد أو الفرع  
     keywords :  
       كم اشترينا هذا الشهر؟  
       المشتريات من المورد فلان  
       اش اشترينا لفرع إب؟  
       تقرير مشتريات الأسبوع  
       كم طلبنا من المورد هذا؟  
       المشتريات الشهرية  
       كم اشترينا من المورد XYZ؟  
       المشتريات حسب التاريخ  
       تقرير الشراء الشهري  
       فواتير الشراء  
       المشتريات من الموردين  
       تقرير المشتريات 
      
     entities :   TheDate    DistributorName    BranchName   
     relatedTables :   tbltemp_Inv_MainInvoice  
    
   
     intent :  get_returned_items  
     description :  عرض المنتجات المرجعة حسب الفرع أو التاريخ  
     keywords :  
       ايش أكثر منتج رجعوه؟  
       المرتجعات الأسبوع الماضي  
       المنتجات اللي رجعوها الناس  
       اش رجعوا العملاء؟  
       كم مرتجعات عندنا؟  
       ايش المنتجات المرتجعة؟  
       المرتجعات هذا الشهر  
       كم منتج رجع؟  
       المنتجات اللي رجعت  
       المرتجعات اليومية  
       كم المنتجات المرتجعة؟  
       المنتجات المعيبة 
      
     entities :   TheDate    ItemName    BranchName   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_product_stock_quantity  
     description :  عرض رصيد المخزون لمنتج معين  
     keywords :  
       كم كمية كولا عندنا؟  
       كم موجود من فانتا؟  
       كم رصيد المنتج 100؟  
       كم متوفر من هذا المنتج؟  
       كم الكمية في المخزن؟  
       كم منتج XYZ لدينا؟  
       كم باقي من هذا الصنف؟  
       كم الكمية المتاحة؟  
       كم الكمية الفعلية؟  
       كم المخزون الحالي؟  
       الكمية القابلة للبيع  
       الرصيد المتاح 
      
     entities :   ItemName    BranchName   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_product_details  
     description :  عرض تفاصيل منتج معين  
     keywords :  
       ما معلومات كولا؟  
       تفاصيل المنتج 100  
       عرض معلومات فانتا  
       بيانات هذا المنتج  
       ما مواصفات هذا الصنف؟  
       معلومات المنتج كاملة  
       عرض تفاصيل المنتج  
       ما سعر هذا المنتج؟  
       كم سعر المنتج؟  
       ما الباركود؟  
       ما تصنيف المنتج؟ 
      
     entities :   ItemName   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_invoice_details  
     description :  عرض تفاصيل فاتورة معينة  
     keywords :  
       ما محتوى الفاتورة 1001؟  
       عرض فاتورة رقم 12345  
       تفاصيل الفاتورة الأخيرة  
       فاتورة العميل محمد  
       عرض الفاتورة الإلكترونية  
       ما في فاتورة 500؟  
       طباعة فاتورة 1000  
       الفاتورة بتاريخ كذا  
       تفاصيل الفاتورة برقم  
       فاتورة المبيعات  
       الفاتورة الكاملة 
      
     entities :   TheNumber    TheDate    ClientName   
     relatedTables :   tbltemp_ItemsMain    tbltemp_Inv_MainInvoice  
    
   
     intent :  get_low_stock_products  
     description :  عرض المنتجات ذات الكمية المنخفضة  
     keywords :  
       ايش المنتجات قليلة الكمية؟  
       كم المنتجات ناقصة؟  
       ايش الصنف اللي قاعد يخلص؟  
       المنتجات تحت الحد الأدنى  
       ايش الحاجات اللي خلت؟  
       المنتجات قربت تنفذ  
       كم المنتجات عندهم كمية قليلة؟  
       المنتجات اللي تحتاج إعادة طلب  
       المنتجات النادرة  
       المنتجات تحت مستوى الأمان  
       تنبيه نفاد المخزون  
       المنتجات المطلوب طلبها 
      
     entities :   BranchName   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_expired_products  
     description :  عرض المنتجات المنتهية الصلاحية  
     keywords :  
       ايش المنتجات المنتهية الصلاحية؟  
       المنتجات اللي خلت الصلاحية  
       ايش الحاجات اللي انتهت؟  
       المنتجات تالفة الصلاحية  
       كم منتج منتهي الصلاحية؟  
       ايش المنتجات اللي ما تصلح؟  
       المنتجات اللي لازم نتخلص منها  
       ايش الصنف اللي خلت مدة صلاحيته؟  
       المنتجات الفاسدة  
       المنتجات غير الصالحة  
       المنتجات التي يجب التخلص منها  
       تنبيه انتهاء الصلاحية 
      
     entities :   TheDate    BranchName   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_branch_performance  
     description :  عرض أداء الفرع من حيث المبيعات  
     keywords :  
       كم أداء فرع صنعاء؟  
       ايش كفاءة الفرع؟  
       كم إيرادات الفرع؟  
       أداء الفرع هذا الشهر  
       كم بيع فرع عدن؟  
       مقارنة أداء الفروع  
       كم فرع إب بيع؟  
       أداء المبيعات بالفرع  
       كم الإيرادات اليومية؟  
       أداء المبيعات الشهرية  
       مقارنة الفروع في المبيعات  
       أفضل فرع مبيعاً 
      
     entities :   BranchName    TheDate   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_client_credit_status  
     description :  عرض حالة الائتمان للعميل  
     keywords :  
       كم ديون العميل محمد؟  
       حالة الائتمان للزبون  
       كم رصيد العميل؟  
       هل العميل عنده دين؟  
       كم الباقي للعميل؟  
       الوضع المالي للزبون  
       هل يمكن للعميل شراء بقية؟  
       كم يمكن للعميل يشتري؟  
       الحد الائتماني للعميل  
       الدين المستحق للعميل  
       الرصيد الائتماني  
       العميل على ذمته كم؟ 
      
     entities :   ClientName   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_product_profit_margin  
     description :  عرض هامش الربح للمنتج  
     keywords :  
       كم ربح المنتج كولا؟  
       ما هامش الربح؟  
       كم الربحية لهذا الصنف؟  
       تحليل الربح للمنتج  
       كم ربحنا من هذا المنتج؟  
       الربح الصافي للمنتج  
       كم الربح بالنسبة للسعر؟  
       تحليل تكلفة وربح المنتج  
       ربحية المنتج بالتفصيل  
       الربح لكل وحدة  
       تحليل الربحية الشاملة  
       هامش الربح المئوي 
      
     entities :   ItemName   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_supplier_invoices  
     description :  عرض فواتير الشراء من مورد معين  
     keywords :  
       ما فواتير المورد XYZ؟  
       فواتير الشراء من المورد  
       كم اشترينا من هذا المورد؟  
       فواتير المورد هذا الشهر  
       عرض فواتير الموردين  
       فواتير الشراء الأخيرة  
       فواتير المورد أحمد  
       فواتير المشتريات من المورد  
       فواتير الموردين المعلقة  
       فواتير الشراء المدفوعة  
       فواتير الموردين حسب التاريخ  
       تقرير فواتير الموردين 
      
     entities :   DistributorName    TheDate   
     relatedTables :   tbltemp_Inv_MainInvoice  
    
   
     intent :  get_product_movement  
     description :  عرض حركة منتج معين في المخازن  
     keywords :  
       ايش حركة كولا؟  
       سجل دخول وخروج المنتج  
       حركة المخزون للصنف  
       كم دخل وخرج من هذا المنتج؟  
       حركة المنتج هذا الشهر  
       سجل الاستلام والتسليم  
       حركة المخزون الشاملة  
       كم منتج XYZ دخل وخرج؟  
       حركة الأصناف  
       سجل المعاملات  
       تقرير حركة المخزون  
       العمليات على المنتج 
      
     entities :   ItemName    TheDate    BranchName   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_branch_inventory  
     description :  عرض المخزون في فرع معين  
     keywords :  
       كم مخزون فرع صنعاء؟  
       عرض محتوى المخزن  
       المنتجات في الفرع  
       كم موجود في المخزن؟  
       تقرير مخزون الفرع  
       المخزون حسب الفرع  
       كم منتج في فرع عدن؟  
       عرض المخزون بالفرع  
       جرد المخزن  
       تقرير الجرد الشهري  
       المخزون الحالي  
       المنتجات المتوفرة 
      
     entities :   BranchName   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_product_barcode  
     description :  عرض الباركود لمنتج معين  
     keywords :  
       ما باركود كولا؟  
       رمز الباركود للمنتج  
       الباركود الخاص بالفانتا  
       رمز المنتج 100  
       عرض الباركود للطباعة  
       الباركود للمنتج  
       رمز الباركود للتغليف  
       الباركود لل.scan  
       طباعة باركود المنتج  
       الباركود للبيع  
       رمز الباركود الرقمي  
       الباركود التجاري 
      
     entities :   ItemName   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_product_reorder_point  
     description :  عرض نقطة إعادة الطلب لمنتج  
     keywords :  
       كم نقطة إعادة الطلب لكولا؟  
       متى نطلب هذا المنتج؟  
       كم الحد الأدنى للمخزون؟  
       نقطة التنبيه للمنتج  
       كم الكمية الحرجة؟  
       متى يطلب المنتج مجدداً؟  
       كم مستوى المخزون الآمن؟  
       نقطة الطلب الآمنة  
       مستوى التنبيه للمخزون  
       الحد الأدنى لطلب المنتج  
       نقطة الطلب الحرجة  
       المستوى الآمن للمخزون 
      
     entities :   ItemName   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_client_sales_history  
     description :  عرض تاريخ المبيعات لعميل معين  
     keywords :  
       ما تاريخ مبيعات العميل محمد؟  
       سجل مشتريات الزبون  
       كم اشترا العميل؟  
       تاريخ الشراء للعميل  
       فواتير العميل السابقة  
       سجل العمليات للعميل  
       كم العميل صرف هذا الشهر؟  
       مشتريات العميل حسب التاريخ  
       سجل المبيعات السنوي  
       تاريخ الفواتير للعميل  
       العميل ومشترياته  
       تقرير العميل الشامل 
      
     entities :   ClientName    TheDate   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  compare_products_sales  
     description :  مقارنة مبيعات منتجين أو أكثر  
     keywords :  
       قارن مبيعات كولا وفانتا  
       ما أفضل منتج بين هذين؟  
       أيهما أكثر مبيعاً كولا أم سبريت؟  
       مقارنة مبيعات المنتجين  
       تحليل أداء المنتجين  
       أيهما أكثر ربحاً؟  
       مقارنة الكمية المباعة  
       تحليل المنتجين التنافسيين  
       أيهما أكثر طلباً؟  
       مقارنة الأداء التجاري  
       تحليل مبيعات المنتجين  
       أيهما أكثر شعبية؟ 
      
     entities :   ItemName    TheDate   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  compare_branches_performance  
     description :  مقارنة أداء فرعين أو أكثر  
     keywords :  
       قارن أداء فرع صنعاء وعدن  
       أيهما أفضل فرع؟  
       ما الفرع الأكثر مبيعاً؟  
       مقارنة إيرادات الفرعين  
       تحليل أداء الفروع  
       أيهما أكثر ربحاً؟  
       مقارنة المبيعات بين الفروع  
       تحليل الفروع التنافسية  
       أيهما أكثر نشاطاً؟  
       مقارنة الأداء التجاري للفروع  
       تحليل مبيعات الفروع  
       أيهما أكثر كفاءة؟ 
      
     entities :   BranchName    TheDate   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  compare_clients_purchases  
     description :  مقارنة مشتريات عميلين أو أكثر  
     keywords :  
       قارن مشتريات العميل أحمد وعلي  
       أيهما أكثر إنفاقاً؟  
       ما العميل الأكثر ولاءً؟  
       مقارنة إنفاق العملاء  
       تحليل أداء العملاء  
       أيهما أكثر شراءً؟  
       مقارنة القيمة المشتراة  
       تحليل العملاء التنافسيين  
       أيهما أكثر نشاطاً؟  
       مقارنة الأداء التجاري للعملاء  
       تحليل مشتريات العملاء  
       أيهما أكثر أهمية؟ 
      
     entities :   ClientName    TheDate   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_product_sales_trend  
     description :  عرض اتجاه مبيعات منتج معين عبر الزمن  
     keywords :  
       ما اتجاه مبيعات كولا؟  
       تحليل مبيعات المنتج 100  
       الاتجاه التجاري لفانتا  
       نمو مبيعات هذا المنتج  
       الاتجاه الشهري للمنتج  
       تحليل أداء المبيعات  
       الاتجاه الموسمي للمنتج  
       مبيعات المنتج عبر الزمن  
       النمو في مبيعات المنتج  
       تحليل الاتجاه التجاري  
       الاتجاهات الموسمية  
       النمو المئوي للمنتج 
      
     entities :   ItemName    TheDate   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_branch_sales_trend  
     description :  عرض اتجاه مبيعات فرع معين عبر الزمن  
     keywords :  
       ما اتجاه مبيعات فرع صنعاء؟  
       تحليل مبيعات الفرع الأول  
       الاتجاه التجاري لفرع عدن  
       نمو مبيعات هذا الفرع  
       الاتجاه الشهري للفرع  
       تحليل أداء مبيعات الفرع  
       الاتجاه الموسمي للفرع  
       مبيعات الفرع عبر الزمن  
       النمو في مبيعات الفرع  
       تحليل الاتجاه التجاري للفرع  
       الاتجاهات الموسمية للفرع  
       النمو المئوي للفرع 
      
     entities :   BranchName    TheDate   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_client_purchase_trend  
     description :  عرض اتجاه مشتريات عميل معين عبر الزمن  
     keywords :  
       ما اتجاه مشتريات العميل محمد؟  
       تحليل إنفاق العميل أحمد  
       الاتجاه التجاري للزبون  
       نمو إنفاق هذا العميل  
       الاتجاه الشهري للعميل  
       تحليل أداء إنفاق العميل  
       الاتجاه الموسمي للعميل  
       إنفاق العميل عبر الزمن  
       النمو في إنفاق العميل  
       تحليل الاتجاه التجاري للعميل  
       الاتجاهات الموسمية للعميل  
       النمو المئوي للعميل 
      
     entities :   ClientName    TheDate   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_monthly_sales_report  
     description :  تقرير المبيعات الشهري  
     keywords :  
       تقرير مبيعات شهر يناير  
       أداء المبيعات لشهر فبراير  
       الإيرادات الشهرية لمارس  
       عرض تقرير المبيعات الشهري  
       تقرير المبيعات لشهر أبريل  
       أداء المبيعات هذا الشهر  
       تقرير الإيرادات الشهرية  
       المبيعات الشهرية بالتفصيل  
       تقرير المبيعات حسب الشهر  
       تحليل المبيعات الشهري  
       تقرير المبيعات الشهري الكامل  
       الإيرادات الشهرية 
      
     entities :   TheDate    BranchName   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_yearly_sales_report  
     description :  تقرير المبيعات السنوي  
     keywords :  
       تقرير مبيعات عام 2024  
       أداء المبيعات للعام الماضي  
       الإيرادات السنوية لعام 2023  
       عرض تقرير المبيعات السنوي  
       تقرير المبيعات لعام 2024  
       أداء المبيعات هذا العام  
       تقرير الإيرادات السنوية  
       المبيعات السنوية بالتفصيل  
       تقرير المبيعات حسب السنة  
       تحليل المبيعات السنوي  
       تقرير المبيعات السنوي الكامل  
       الإيرادات السنوية 
      
     entities :   TheDate    BranchName   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_daily_sales_report  
     description :  تقرير المبيعات اليومي  
     keywords :  
       تقرير مبيعات اليوم  
       أداء المبيعات اليومي  
       الإيرادات اليومية  
       عرض تقرير المبيعات اليومي  
       تقرير المبيعات لهذا اليوم  
       أداء المبيعات اليوم  
       تقرير الإيرادات اليومية  
       المبيعات اليومية بالتفصيل  
       تقرير المبيعات حسب اليوم  
       تحليل المبيعات اليومي  
       تقرير المبيعات اليومي الكامل  
       الإيرادات اليوم 
      
     entities :   TheDate    BranchName   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_product_category_sales  
     description :  تقرير مبيعات حسب تصنيف المنتج  
     keywords :  
       كم مبيعات فئة المشروبات؟  
       مبيعات التصنيف الغذائي  
       تقرير المبيعات حسب الفئة  
       أداء فئات المنتجات  
       تحليل مبيعات التصنيفات  
       كم مبيعات فئة الأدوية؟  
       مبيعات فئة التنظيف  
       تقرير الفئات التجارية  
       تحليل أداء التصنيفات  
       أفضل فئات مبيعاً  
       مبيعات الفئات المختلفة  
       تقرير التصنيفات 
      
     entities :   CategoryName    TheDate   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_branch_category_sales  
     description :  تقرير مبيعات الفرع حسب تصنيف المنتج  
     keywords :  
       كم مبيعات فرع صنعاء حسب الفئة؟  
       مبيعات الفرع الأول حسب التصنيف  
       تقرير فرع عدن حسب الفئة  
       أداء فرع إب في التصنيفات  
       تحليل مبيعات الفرع بالتصنيف  
       كم مبيعات فرع حضرموت حسب الفئة؟  
       مبيعات فرع تعز حسب التصنيف  
       تقرير الفرع حسب الفئات  
       تحليل أداء الفرع بالتصنيف  
       أفضل فئات فرع صنعاء  
       مبيعات الفرع بالتصنيفات  
       تقرير الفرع والتصنيف 
      
     entities :   BranchName    CategoryName    TheDate   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_client_category_purchases  
     description :  تقرير مشتريات العميل حسب تصنيف المنتج  
     keywords :  
       كم اشترا العميل محمد حسب الفئة؟  
       مشتريات العميل أحمد حسب التصنيف  
       تقرير العميل علي حسب الفئة  
       أداء العميل في التصنيفات  
       تحليل مشتريات العميل بالتصنيف  
       كم اشترا العميل حسب الفئة؟  
       مشتريات العميل حسب التصنيف  
       تقرير العميل حسب الفئات  
       تحليل أداء العميل بالتصنيف  
       أفضل فئات العميل محمد  
       مشتريات العميل بالتصنيفات  
       تقرير العميل والتصنيف 
      
     entities :   ClientName    CategoryName    TheDate   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_product_price_analysis  
     description :  تحليل أسعار المنتجات  
     keywords :  
       ما سعر المنتج كولا؟  
       تحليل أسعار المنتج 100  
       مقارنة أسعار المنتجين  
       سعر المنتج بالجملة  
       سعر المنتج للمستهلك  
       تحليل الأسعار التجارية  
       سعر المنتج حسب التاريخ  
       مقارنة الأسعار بالسوق  
       تحليل الأسعار التنافسية  
       سعر المنتج الحالي  
       قائمة الأسعار الحالية  
       تحليل الأسعار الشامل 
      
     entities :   ItemName    TheDate   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_branch_expense_analysis  
     description :  تحليل مصاريف الفرع  
     keywords :  
       كم مصاريف فرع صنعاء؟  
       تحليل نفقات الفرع الأول  
       مصاريف فرع عدن الشهرية  
       تقرير مصاريف الفرع  
       تحليل النفقات التجارية  
       مصاريف الفرع حسب التاريخ  
       مقارنة مصاريف الفروع  
       تحليل المصاريف التنافسية  
       مصاريف الفرع التشغيلية  
       مصاريف الفرع الحالية  
       قائمة المصاريف الحالية  
       تحليل المصاريف الشامل 
      
     entities :   BranchName    TheDate   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_client_payment_analysis  
     description :  تحليل مدفوعات العميل  
     keywords :  
       كم مدفوعات العميل محمد؟  
       تحليل دفعات العميل أحمد  
       مدفوعات العميل علي الشهرية  
       تقرير مدفوعات العميل  
       تحليل الدفعات التجارية  
       مدفوعات العميل حسب التاريخ  
       مقارنة مدفوعات العملاء  
       تحليل المدفوعات التنافسية  
       مدفوعات العميل النقدية  
       مدفوعات العميل الحالية  
       قائمة المدفوعات الحالية  
       تحليل المدفوعات الشامل 
      
     entities :   ClientName    TheDate   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_supplier_payment_analysis  
     description :  تحليل مدفوعات المورد  
     keywords :  
       كم مدفوعات المورد XYZ؟  
       تحليل دفعات المورد أحمد  
       مدفوعات المورد علي الشهرية  
       تقرير مدفوعات المورد  
       تحليل الدفعات التجارية  
       مدفوعات المورد حسب التاريخ  
       مقارنة مدفوعات الموردين  
       تحليل المدفوعات التنافسية  
       مدفوعات المورد النقدية  
       مدفوعات المورد الحالية  
       قائمة المدفوعات الحالية  
       تحليل المدفوعات الشامل 
      
     entities :   DistributorName    TheDate   
     relatedTables :   tbltemp_Inv_MainInvoice  
    
   
     intent :  get_inventory_turnover_analysis  
     description :  تحليل معدل دوران المخزون  
     keywords :  
       كم معدل دوران كولا؟  
       تحليل دوران المنتج 100  
       معدل التدوير التجاري  
       دوران المخزون في الفرع  
       تحليل كفاءة المخزون  
       معدل دوران المخازن  
       تحليل التدوير التجاري  
       دوران المخزون الشهري  
       معدل التدوير السنوي  
       دوران المخزون الحالي  
       تحليل دوران المخزون الشامل  
       معدل التدوير الكامل 
      
     entities :   ItemName    BranchName    TheDate   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_product_demand_forecast  
     description :  توقع الطلب على منتج معين  
     keywords :  
       كم طلب كولا الشهر القادم؟  
       توقع الطلب للمنتج 100  
       تحليل الطلب المستقبلي  
       توقع المبيعات التجارية  
       طلب المنتج حسب التاريخ  
       مقارنة الطلب المتوقع  
       تحليل الطلب التنافسي  
       طلب المنتج المستقبلي  
       طلب المنتج الحالي  
       قائمة الطلبات المتوقعة  
       تحليل الطلب الشامل  
       توقع الطلب الشهري 
      
     entities :   ItemName    TheDate   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_branch_demand_forecast  
     description :  توقع الطلب على فرع معين  
     keywords :  
       كم طلب فرع صنعاء الشهر القادم؟  
       توقع الطلب للفرع الأول  
       تحليل الطلب المستقبلي للفرع  
       توقع مبيعات الفرع التجاري  
       طلب الفرع حسب التاريخ  
       مقارنة الطلب المتوقع للفروع  
       تحليل الطلب التنافسي للفرع  
       طلب الفرع المستقبلي  
       طلب الفرع الحالي  
       قائمة طلبات الفرع المتوقعة  
       تحليل الطلب الشامل للفرع  
       توقع الطلب الشهري للفرع 
      
     entities :   BranchName    TheDate   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_client_demand_forecast  
     description :  توقع الطلب من عميل معين  
     keywords :  
       كم طلب العميل محمد الشهر القادم؟  
       توقع الطلب للعميل أحمد  
       تحليل الطلب المستقبلي للعميل  
       توقع مشتريات العميل التجاري  
       طلب العميل حسب التاريخ  
       مقارنة الطلب المتوقع للعملاء  
       تحليل الطلب التنافسي للعميل  
       طلب العميل المستقبلي  
       طلب العميل الحالي  
       قائمة طلبات العميل المتوقعة  
       تحليل الطلب الشامل للعميل  
       توقع الطلب الشهري للعميل 
      
     entities :   ClientName    TheDate   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_seasonal_trend_analysis  
     description :  تحليل الاتجاهات الموسمية  
     keywords :  
       ما الاتجاهات الموسمية في المبيعات؟  
       المواسم التجارية لهذا العام  
       الاتجاهات الزمنية للمنتجات  
       المواسم ذات الطلب العالي  
       عرض الاتجاهات الدورية  
       المواسم التي تزيد فيها المبيعات  
       تحليل الاتجاهات الموسمية  
       المنتجات حسب الموسم  
       الاتجاهات الموسمية للعام الماضي  
       المواسم التجارية الناجحة  
       تحليل المواسم التجارية  
       الاتجاهات الموسمية الشاملة 
      
     entities :   TheDate    ItemName   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_promotional_effect_analysis  
     description :  تحليل تأثير العروض الترويجية  
     keywords :  
       كم زادت المبيعات بالعرض؟  
       تحليل تأثير الحملة الترويجية  
       العروض التجارية لهذا الشهر  
       تأثير الخصومات على المبيعات  
       عرض الاتجاهات الترويجية  
       العروض التي زادت المبيعات  
       تحليل تأثير العروض  
       المنتجات حسب العرض  
       تأثير العروض للعام الماضي  
       العروض التجارية الناجحة  
       تحليل العروض التجارية  
       تأثير العروض الشامل 
      
     entities :   TheDate    ItemName   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_customer_satisfaction_analysis  
     description :  تحليل رضا العملاء  
     keywords :  
       كم رضا العملاء عن كولا؟  
       تحليل رضا العملاء للمنتج 100  
       تقييمات العملاء التجارية  
       رضا العملاء حسب التاريخ  
       عرض التقييمات الإيجابية  
       المنتجات ذات الرضا العالي  
       تحليل رضا العملاء  
       المنتجات حسب التقييم  
       رضا العملاء للعام الماضي  
       المنتجات التجارية الناجحة  
       تحليل رضا العملاء الشامل  
       تقييمات العملاء الشاملة 
      
     entities :   ClientName    ItemName    TheDate   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_competitor_analysis  
     description :  تحليل المنافسة في السوق  
     keywords :  
       ما المنتجات المنافسة لكولا؟  
       المنافسون في السوق  
       المنافسة التجارية للمنتج  
       تحليل السوق التنافسي  
       المنتجات المماثلة  
       عرض المنافسون الرئيسيون  
       المنافسة حسب السعر  
       تحليل المنافسة في السوق  
       المنتجات المنافسة حسب النوع  
       مقارنة المنتجات المنافسة  
       تحليل المنافسة الشامل  
       المنافسة التجارية الشاملة 
      
     entities :   ItemName   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_market_share_analysis  
     description :  تحليل حصة السوق  
     keywords :  
       كم حصة السوق لكولا؟  
       تحليل حصة السوق للمنتج 100  
       الحصة السوقية التجارية  
       حصة السوق حسب التاريخ  
       عرض الحصة السوقية  
       المنتجات ذات الحصة الأعلى  
       تحليل الحصة السوقية  
       المنتجات حسب الحصة  
       حصة السوق للعام الماضي  
       المنتجات التجارية الناجحة  
       تحليل الحصة السوقية الشامل  
       الحصة السوقية الشاملة 
      
     entities :   ItemName    TheDate   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_financial_performance_analysis  
     description :  تحليل الأداء المالي  
     keywords :  
       ما الأداء المالي للشركة؟  
       تحليل الأداء المالي الشهري  
       الأداء المالي التجاري  
       الأداء المالي حسب التاريخ  
       عرض الأداء المالي  
       الفرع ذو الأداء الأعلى  
       تحليل الأداء المالي  
       الأداء المالي حسب الفرع  
       الأداء المالي للعام الماضي  
       الشركة التجارية الناجحة  
       تحليل الأداء المالي الشامل  
       الأداء المالي الشامل 
      
     entities :   BranchName    TheDate   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_budget_vs_actual_analysis  
     description :  تحليل الميزانية مقابل الفعلي  
     keywords :  
       قارن الميزانية الفعلية مع المخططة  
       الإنفاق الفعلي لشهر يناير  
       الميزانية المخططة للعام  
       عرض مقارنة الميزانيات  
       الإنفاق الفعلي حسب القسم  
       تحليل الميزانية التشغيلية  
       مقارنة الإنفاق الشهري  
       الميزانية الفعلية للفرع  
       تقرير الميزانية مقابل الفعلي  
       الإنفاق مقابل الميزانية  
       تحليل الميزانية الشامل  
       الميزانية مقابل الفعلي الشامل 
      
     entities :   BranchName    TheDate   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_key_performance_indicators  
     description :  تحليل المؤشرات الرئيسية للأداء  
     keywords :  
       عرض المؤشرات الرئيسية للأداء  
       KPIs للشركة هذا الشهر  
       مؤشرات الأداء المؤسسي  
       القياسات الأساسية للنشاط  
       عرض KPIs حسب القسم  
       تحليل المؤشرات الرئيسية  
       مؤشرات الأداء الشهري  
       KPIs حسب الفرع  
       تقرير المؤشرات الأساسية  
       القياسات المؤسسية  
       تحليل KPIs الشامل  
       المؤشرات الرئيسية الشاملة 
      
     entities :   BranchName    TheDate   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_supply_chain_analysis  
     description :  تحليل سلسلة التوريد  
     keywords :  
       ما حالة سلسلة التوريد؟  
       تحليل سلسلة التوريد الشهري  
       سلسلة التوريد التجارية  
       سلسلة التوريد حسب التاريخ  
       عرض سلسلة التوريد  
       الموردون ذوو الأداء الأعلى  
       تحليل سلسلة التوريد  
       سلسلة التوريد حسب المورد  
       سلسلة التوريد للعام الماضي  
       الشركة التجارية الناجحة  
       تحليل سلسلة التوريد الشامل  
       سلسلة التوريد الشاملة 
      
     entities :   DistributorName    TheDate   
     relatedTables :   tbltemp_Inv_MainInvoice  
    
   
     intent :  get_quality_control_analysis  
     description :  تحليل جودة المنتجات  
     keywords :  
       كم المنتجات المعيبة؟  
       تحليل جودة المنتجات الشهري  
       جودة المنتجات التجارية  
       جودة المنتجات حسب التاريخ  
       عرض جودة المنتجات  
       المنتجات ذات الجودة الأعلى  
       تحليل جودة المنتجات  
       المنتجات حسب الجودة  
       جودة المنتجات للعام الماضي  
       المنتجات التجارية الناجحة  
       تحليل جودة المنتجات الشامل  
       جودة المنتجات الشاملة 
      
     entities :   ItemName    TheDate   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_employee_performance_analysis  
     description :  تحليل أداء الموظفين  
     keywords :  
       ما أداء الموظفين؟  
       تحليل أداء الموظفين الشهري  
       أداء الموظفين التجاري  
       أداء الموظفين حسب التاريخ  
       عرض أداء الموظفين  
       الموظفون ذوو الأداء الأعلى  
       تحليل أداء الموظفين  
       أداء الموظفين حسب القسم  
       أداء الموظفين للعام الماضي  
       الشركة التجارية الناجحة  
       تحليل أداء الموظفين الشامل  
       أداء الموظفين الشامل 
      
     entities :   BranchName    TheDate   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_customer_retention_analysis  
     description :  تحليل احتفاظ العملاء  
     keywords :  
       كم احتفاظ العملاء؟  
       تحليل احتفاظ العملاء الشهري  
       احتفاظ العملاء التجاري  
       احتفاظ العملاء حسب التاريخ  
       عرض احتفاظ العملاء  
       العملاء ذوو الاحتفاظ الأعلى  
       تحليل احتفاظ العملاء  
       احتفاظ العملاء حسب القسم  
       احتفاظ العملاء للعام الماضي  
       الشركة التجارية الناجحة  
       تحليل احتفاظ العملاء الشامل  
       احتفاظ العملاء الشامل 
      
     entities :   ClientName    TheDate   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_product_lifecycle_analysis  
     description :  تحليل دورة حياة المنتج  
     keywords :  
       ما دورة حياة كولا؟  
       تحليل دورة حياة المنتج 100  
       دورة حياة المنتج التجاري  
       دورة حياة المنتج حسب التاريخ  
       عرض دورة حياة المنتج  
       المنتجات في مرحلة النمو  
       تحليل دورة حياة المنتج  
       المنتجات حسب المرحلة  
       دورة حياة المنتج للعام الماضي  
       المنتجات التجارية الناجحة  
       تحليل دورة حياة المنتج الشامل  
       دورة حياة المنتج الشاملة 
      
     entities :   ItemName    TheDate   
     relatedTables :   tbltemp_ItemsMain  
    
   
     intent :  get_innovation_opportunities_analysis  
     description :  تحليل فرص الابتكار  
     keywords :  
       ما فرص الابتكار؟  
       تحليل فرص الابتكار الشهري  
       فرص الابتكار التجارية  
       فرص الابتكار حسب التاريخ  
       عرض فرص الابتكار  
       الفرص ذات الإمكانية الأعلى  
       تحليل فرص الابتكار  
       فرص الابتكار حسب القسم  
       فرص الابتكار للعام الماضي  
       الشركة التجارية الناجحة  
       تحليل فرص الابتكار الشامل  
       فرص الابتكار الشاملة 
      
     entities :   ItemName    TheDate   
     relatedTables :   tbltemp_ItemsMain  
   
 