قواعد العمل والمنطق التجاري للنظام

هذا الملف يحتوي على القواعد التجارية والمنطق الخاص بالنظام.

قواعد المنتجات:

1. حالة المنتج النشط
- المنتج النشط: ISActive = 1
- المنتج غير النشط: ISActive = 0
- يجب عرض المنتجات النشطة فقط في التقارير العادية

2. إدارة المخزون
- نقطة إعادة الطلب: ReorderPoint
- عندما Quantity <= ReorderPoint يجب التنبيه
- المنتجات النافدة: Quantity = 0
- المنتجات قليلة المخزون: Quantity <= ReorderPoint AND Quantity > 0

3. تواريخ الصلاحية
- المنتجات ذات الصلاحية: ISExpiry = 1
- المنتجات منتهية الصلاحية: ExpiryDate < GETDATE() AND ISExpiry = 1
- المنتجات قريبة الانتهاء: ExpiryDate <= DATEADD(DAY, 30, GETDATE()) AND ISExpiry = 1

4. تصنيف المنتجات
- كل منتج ينتمي لفئة: CategoryID
- الفئات لها تسلسل هرمي: ParentCategoryID
- يمكن البحث حسب الفئة أو الفئة الأب

قواعد المبيعات:

1. أنواع المستندات
- فاتورة مبيعات: DocumentName = 'فاتورة مبيعات'
- فاتورة مشتريات: DocumentName = 'فاتورة مشتريات'
- فاتورة مرتجعات: DocumentName = 'فاتورة مرتجعات'

2. حساب المبيعات
- المبيعات = SUM(Amount) WHERE DocumentName = 'فاتورة مبيعات'
- الكمية المباعة = SUM(Quantity) WHERE DocumentName = 'فاتورة مبيعات'
- متوسط سعر البيع = SUM(Amount) / SUM(Quantity)

3. تحليل الأداء
- أفضل منتج = أعلى SUM(Quantity) أو SUM(Amount)
- أفضل عميل = أعلى SUM(Amount) لكل ClientName
- أفضل فرع = أعلى SUM(Amount) لكل BranchName

قواعد العملاء:

1. تصنيف العملاء
- عميل جديد: أول عملية شراء خلال آخر 3 أشهر
- عميل نشط: آخر عملية شراء خلال آخر 6 أشهر
- عميل خامل: آخر عملية شراء أكثر من 6 أشهر

2. قيمة العميل
- إجمالي مشتريات العميل = SUM(Amount) لكل ClientID
- متوسط قيمة الشراء = SUM(Amount) / COUNT(DISTINCT InvoiceID)
- تكرار الشراء = COUNT(DISTINCT InvoiceID)

3. ولاء العميل
- عميل مخلص: أكثر من 10 عمليات شراء
- عميل متوسط: 3-10 عمليات شراء
- عميل جديد: 1-2 عمليات شراء

قواعد الفروع:

1. أداء الفروع
- مبيعات الفرع = SUM(Amount) WHERE BranchName = 'اسم الفرع'
- عدد العملاء = COUNT(DISTINCT ClientID) لكل فرع
- متوسط قيمة المعاملة = SUM(Amount) / COUNT(*)

2. مقارنة الفروع
- أفضل فرع = أعلى مبيعات
- أسوأ فرع = أقل مبيعات
- معدل النمو = (مبيعات الفترة الحالية - مبيعات الفترة السابقة) / مبيعات الفترة السابقة * 100

قواعد التواريخ والفترات:

1. الفترات الزمنية المعيارية
- اليوم: CAST(GETDATE() AS DATE)
- الأسبوع الحالي: DATEPART(WEEK, TheDate) = DATEPART(WEEK, GETDATE())
- الشهر الحالي: MONTH(TheDate) = MONTH(GETDATE()) AND YEAR(TheDate) = YEAR(GETDATE())
- السنة الحالية: YEAR(TheDate) = YEAR(GETDATE())

2. المقارنات الزمنية
- نفس الفترة العام الماضي: DATEADD(YEAR, -1, تاريخ البداية) إلى DATEADD(YEAR, -1, تاريخ النهاية)
- الفترة السابقة: حسب نوع الفترة (يوم/أسبوع/شهر/سنة)

قواعد التقارير:

1. تقارير المبيعات
- يجب تضمين فقط فواتير المبيعات
- استبعاد المرتجعات إلا إذا طُلب صراحة
- عرض النتائج مرتبة حسب القيمة أو التاريخ

2. تقارير المخزون
- عرض المنتجات النشطة فقط
- تضمين تنبيهات نقطة إعادة الطلب
- عرض تواريخ الصلاحية للمنتجات ذات الصلاحية

3. تقارير العملاء
- ترتيب حسب قيمة المشتريات أو تاريخ آخر شراء
- تضمين معلومات الاتصال إذا متوفرة
- تصنيف العملاء حسب النشاط

قواعد الأمان والصلاحيات:

1. حماية البيانات الحساسة
- عدم عرض معلومات مالية تفصيلية للمستخدمين غير المخولين
- إخفاء معلومات العملاء الشخصية
- تشفير البيانات الحساسة

2. صلاحيات الوصول
- مدير النظام: وصول كامل
- مدير المبيعات: تقارير المبيعات والعملاء
- موظف المخزون: تقارير المخزون والمنتجات
- محاسب: التقارير المالية

قواعد جودة البيانات:

1. التحقق من صحة البيانات
- الأسعار يجب أن تكون أكبر من صفر
- الكميات يجب أن تكون أرقام صحيحة موجبة
- التواريخ يجب أن تكون صحيحة ومنطقية

2. معالجة البيانات المفقودة
- استخدام قيم افتراضية للحقول الفارغة
- تنبيه المستخدم للبيانات المفقودة المهمة
- تجاهل السجلات غير المكتملة في التحليلات

قواعد الأداء:

1. تحسين الاستعلامات
- استخدام TOP لتحديد عدد النتائج
- إضافة فلاتر زمنية لتقليل البيانات المعالجة
- استخدام الفهارس المناسبة

2. إدارة الذاكرة
- تجنب تحميل جداول كاملة
- استخدام التصفح للنتائج الكبيرة
- تنظيف البيانات المؤقتة بانتظام

قواعد واجهة المستخدم:

1. عرض النتائج
- ترقيم النتائج للسهولة
- عرض ملخص قبل التفاصيل
- استخدام ألوان للتمييز بين أنواع البيانات

2. رسائل التنبيه
- رسائل واضحة ومفهومة
- اقتراح حلول للمشاكل
- تأكيد العمليات المهمة

قواعد النسخ الاحتياطي:

1. حفظ البيانات
- نسخ احتياطية يومية للبيانات المهمة
- حفظ سجل العمليات
- اختبار استعادة البيانات بانتظام

2. استعادة النظام
- خطة واضحة لاستعادة النظام
- نسخ احتياطية في مواقع متعددة
- توثيق إجراءات الاستعادة

قواعد الصيانة:

1. تنظيف البيانات
- حذف البيانات القديمة غير المهمة
- ضغط الجداول الكبيرة
- إعادة بناء الفهارس بانتظام

2. مراقبة الأداء
- تتبع أوقات الاستجابة
- مراقبة استخدام الذاكرة والمعالج
- تحليل الاستعلامات البطيئة

هذه القواعد يجب مراعاتها في جميع العمليات والتقارير لضمان دقة وموثوقية النظام.
